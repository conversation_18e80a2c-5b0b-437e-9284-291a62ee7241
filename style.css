* {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Microsoft YaHei", "PingFang SC", "Helvetica Neue", Arial, sans-serif;
        }
        
        body {
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e6e9ed;
        }
        
        .header h1 {
            color: #2c3e50;
            font-size: 24px;
            font-weight: 500;
        }
        
        .header .actions {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 8px 16px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background-color: #1890ff;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #40a9ff;
        }
        
        .btn-outline {
            background-color: transparent;
            border: 1px solid #d9d9d9;
            color: #666;
        }
        
        .btn-outline:hover {
            border-color: #1890ff;
            color: #1890ff;
        }
        
        .filters {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 25px;
            padding: 15px;
            background-color: white;
            border-radius: 6px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
        }
        
        .filter-item {
            display: flex;
            flex-direction: column;
        }
        
        .filter-label {
            font-size: 13px;
            color: #666;
            margin-bottom: 5px;
        }
        
        .filter-input {
            padding: 8px 10px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            min-width: 150px;
            font-size: 14px;
        }
        
        .filter-input:focus {
            border-color: #1890ff;
            outline: none;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 25px;
        }
        
        .card {
            background-color: white;
            border-radius: 6px;
            padding: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
        }
        
        .card-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }
        
        .card-value {
            font-size: 24px;
            font-weight: 500;
            color: #2c3e50;
        }
        
        .card-trend {
            font-size: 14px;
            margin-top: 5px;
        }
        
        .trend-up {
            color: #f5222d;
        }
        
        .trend-down {
            color: #52c41a;
        }
        
        .chart-container {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 25px;
        }
        
        .chart {
            background-color: white;
            border-radius: 6px;
            padding: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
            height: 350px;
        }
        
        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .chart-title {
            font-size: 16px;
            font-weight: 500;
            color: #2c3e50;
        }
        
        .chart-legend {
            display: flex;
            gap: 15px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            font-size: 13px;
            color: #666;
        }
        
        .legend-color {
            width: 12px;
            height: 12px;
            margin-right: 5px;
            border-radius: 2px;
        }
        
        .legend-revenue {
            background-color: #1890ff;
        }
        
        .legend-cost {
            background-color: #ffc53d;
        }
        
        .legend-profit {
            background-color: #52c41a;
        }
        
        .chart-content {
            height: calc(100% - 35px);
            position: relative;
        }
        
        .table-container {
            background-color: white;
            border-radius: 6px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
            overflow: auto;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px; /* 减小字体以适应更多列 */
        }

        .data-table th,
        .data-table td {
            padding: 8px 6px; /* 减小内边距 */
            text-align: left;
            border-bottom: 1px solid #e6e9ed;
            white-space: nowrap; /* 防止文字换行 */
            min-width: 70px; /* 设置最小宽度 */
        }

        .data-table th {
            background-color: #f9fafb;
            color: #666;
            font-weight: 500;
            font-size: 11px;
            cursor: pointer; /* 添加手型光标 */
            user-select: none; /* 防止选中文本 */
            position: sticky;
            top: 0;
            z-index: 10;
        }

        /* 特定列的样式优化 */
        .data-table th[data-column="name"],
        .data-table td:first-child {
            min-width: 120px;
            max-width: 150px;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .data-table th[data-column="platform"],
        .data-table td:nth-child(2) {
            min-width: 80px;
            text-align: center;
        }

        /* 金额列右对齐 */
        .data-table th[data-column$="Fee"],
        .data-table th[data-column$="Cost"],
        .data-table th[data-column="revenue"],
        .data-table th[data-column="totalCost"],
        .data-table th[data-column="netProfit"] {
            text-align: right;
        }

        /* 百分比列居中 */
        .data-table th[data-column$="Margin"],
        .data-table th[data-column$="Rate"] {
            text-align: center;
        }

        .data-table th .sort-indicator {
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 12px;
            color: #999;
        }
        
        .data-table tr:hover {
            background-color: #f5f7fa;
        }
        
        .pagination {
            display: flex;
            justify-content: flex-end;
            margin-top: 20px;
            gap: 10px;
        }
        
        .page-btn {
            padding: 6px 12px;
            border: 1px solid #d9d9d9;
            background-color: white;
            border-radius: 4px;
            cursor: pointer;
            color: #666;
            font-size: 14px;
        }
        
        .page-btn.active {
            background-color: #1890ff;
            border-color: #1890ff;
            color: white;
        }
        
        .page-btn:hover:not(.active) {
            border-color: #1890ff;
            color: #1890ff;
        }
        
        /* Tab导航栏样式 */
        .tabs {
            display: flex;
            border-bottom: 1px solid #e0e0e0;
            margin-bottom: 20px;
            background: #fafbfc;
        }
        .tab-btn {
            padding: 12px 32px;
            border: none;
            background: none;
            cursor: pointer;
            font-size: 16px;
            color: #666;
            outline: none;
            transition: color 0.2s, border-bottom 0.2s;
            border-bottom: 2px solid transparent;
        }
        .tab-btn.active {
            color: #1976d2;
            border-bottom: 2px solid #1976d2;
            background: #fff;
        }
        .tab-content {
            display: none;
        }
        .tab-content[style*="display: block"] {
            display: block;
        }

        /* 今日数据看板样式 */
        .today-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px 0;
            border-bottom: 1px solid #e6e9ed;
        }

        .today-header h2 {
            color: #2c3e50;
            font-size: 20px;
            font-weight: 500;
            margin: 0;
        }

        .today-date {
            color: #666;
            font-size: 14px;
        }

        .today-alerts {
            background-color: white;
            border-radius: 6px;
            padding: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
            margin-top: 20px;
        }

        .today-alerts h3 {
            color: #2c3e50;
            font-size: 16px;
            font-weight: 500;
            margin: 0 0 15px 0;
        }

        .alert-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .alert-item {
            display: flex;
            align-items: center;
            padding: 10px 15px;
            border-radius: 4px;
            font-size: 14px;
        }

        .alert-item.warning {
            background-color: #fff7e6;
            border-left: 4px solid #ffc53d;
            color: #d46b08;
        }

        .alert-item.success {
            background-color: #f6ffed;
            border-left: 4px solid #52c41a;
            color: #389e0d;
        }

        .alert-item.info {
            background-color: #e6f7ff;
            border-left: 4px solid #1890ff;
            color: #096dd9;
        }

        .alert-icon {
            margin-right: 8px;
            font-weight: bold;
        }

        /* 产品管理样式 */
        .products-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px 0;
            border-bottom: 1px solid #e6e9ed;
        }

        .products-header h2 {
            color: #2c3e50;
            font-size: 20px;
            font-weight: 500;
            margin: 0;
        }

        .products-filters {
            display: flex;
            gap: 15px;
            align-items: end;
            margin-bottom: 20px;
            padding: 15px;
            background-color: white;
            border-radius: 6px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .product-card {
            background-color: white;
            border-radius: 6px;
            padding: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.1);
        }

        .product-card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .product-name {
            font-size: 16px;
            font-weight: 500;
            color: #2c3e50;
            margin: 0;
            flex: 1;
            margin-right: 10px;
        }

        .product-actions {
            display: flex;
            gap: 5px;
        }

        .product-actions button {
            padding: 4px 8px;
            font-size: 12px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .edit-btn {
            background-color: #1890ff;
            color: white;
        }

        .edit-btn:hover {
            background-color: #40a9ff;
        }

        .delete-btn {
            background-color: #ff4d4f;
            color: white;
        }

        .delete-btn:hover {
            background-color: #ff7875;
        }

        .product-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }

        .product-info-item {
            display: flex;
            flex-direction: column;
        }

        .product-info-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 2px;
        }

        .product-info-value {
            font-size: 14px;
            color: #2c3e50;
            font-weight: 500;
        }

        .product-metrics {
            display: flex;
            justify-content: space-between;
            padding-top: 15px;
            border-top: 1px solid #f0f0f0;
        }

        .product-metric {
            text-align: center;
        }

        .product-metric-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 2px;
        }

        .product-metric-value {
            font-size: 14px;
            font-weight: 500;
        }

        .profit-positive {
            color: #52c41a;
        }

        .profit-negative {
            color: #ff4d4f;
        }

        /* 模态框样式 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background-color: white;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 20px 0 20px;
            border-bottom: 1px solid #f0f0f0;
            margin-bottom: 20px;
        }

        .modal-header h3 {
            margin: 0;
            color: #2c3e50;
            font-size: 18px;
            font-weight: 500;
        }

        .modal-close {
            font-size: 24px;
            color: #999;
            cursor: pointer;
            line-height: 1;
            padding: 0 0 20px 0;
        }

        .modal-close:hover {
            color: #666;
        }

        .modal-body {
            padding: 0 20px 20px 20px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-size: 14px;
            color: #333;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-group input,
        .form-group select {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.2s;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #f0f0f0;
        }

        /* 客户管理样式 */
        .customers-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px 0;
            border-bottom: 1px solid #e6e9ed;
        }

        .customers-header h2 {
            color: #2c3e50;
            font-size: 20px;
            font-weight: 500;
            margin: 0;
        }

        .customers-stats {
            display: flex;
            gap: 30px;
        }

        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }

        .stat-value {
            font-size: 18px;
            font-weight: 500;
            color: #2c3e50;
        }

        .customers-filters {
            display: flex;
            gap: 15px;
            align-items: end;
            margin-bottom: 20px;
            padding: 15px;
            background-color: white;
            border-radius: 6px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
        }

        .customers-table-container {
            background-color: white;
            border-radius: 6px;
            padding: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
            overflow-x: auto;
        }

        .customers-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .customers-table th,
        .customers-table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }

        .customers-table th {
            background-color: #fafafa;
            font-weight: 500;
            color: #333;
            position: sticky;
            top: 0;
        }

        .customers-table tr:hover {
            background-color: #f9f9f9;
        }

        .customer-value-high {
            color: #52c41a;
            font-weight: 500;
        }

        .customer-value-medium {
            color: #1890ff;
            font-weight: 500;
        }

        .customer-value-low {
            color: #faad14;
            font-weight: 500;
        }

        /* 通知和错误提示样式 */
        .notification-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 2000;
            max-width: 400px;
        }

        .notification {
            background-color: white;
            border-radius: 6px;
            padding: 15px 20px;
            margin-bottom: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            border-left: 4px solid;
            display: flex;
            align-items: center;
            justify-content: space-between;
            animation: slideIn 0.3s ease-out;
        }

        .notification.success {
            border-left-color: #52c41a;
            background-color: #f6ffed;
        }

        .notification.error {
            border-left-color: #ff4d4f;
            background-color: #fff2f0;
        }

        .notification.warning {
            border-left-color: #faad14;
            background-color: #fffbe6;
        }

        .notification.info {
            border-left-color: #1890ff;
            background-color: #e6f7ff;
        }

        .notification-content {
            flex: 1;
            display: flex;
            align-items: center;
        }

        .notification-icon {
            margin-right: 10px;
            font-size: 16px;
        }

        .notification-close {
            cursor: pointer;
            color: #999;
            font-size: 18px;
            margin-left: 10px;
        }

        .notification-close:hover {
            color: #666;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOut {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        .error-message {
            color: #ff4d4f;
            font-size: 12px;
            margin-top: 4px;
            min-height: 16px;
        }

        .form-group.error input,
        .form-group.error select {
            border-color: #ff4d4f;
            box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
        }

        .form-group.success input,
        .form-group.success select {
            border-color: #52c41a;
            box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 3000;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #1890ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 高级筛选样式 */
        .advanced-filter-container {
            max-height: 60vh;
            overflow-y: auto;
        }

        .filter-group {
            margin-bottom: 25px;
            padding: 15px;
            border: 1px solid #f0f0f0;
            border-radius: 6px;
            background-color: #fafafa;
        }

        .filter-group h4 {
            margin: 0 0 15px 0;
            color: #2c3e50;
            font-size: 16px;
            font-weight: 500;
        }

        .range-inputs {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .range-inputs input {
            flex: 1;
            padding: 6px 10px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
        }

        .range-inputs span {
            color: #666;
            font-size: 14px;
        }

        .checkbox-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .checkbox-group label {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            cursor: pointer;
        }

        .checkbox-group input[type="checkbox"] {
            margin: 0;
        }

        .saved-filters-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .saved-filter-item {
            padding: 15px;
            border: 1px solid #e6e9ed;
            border-radius: 6px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .saved-filter-item:hover {
            background-color: #f9f9f9;
        }

        .saved-filter-item.selected {
            background-color: #e6f7ff;
            border-color: #1890ff;
        }

        .saved-filter-name {
            font-weight: 500;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .saved-filter-description {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
        }

        .saved-filter-meta {
            font-size: 11px;
            color: #999;
            display: flex;
            justify-content: space-between;
        }

        .saved-filter-actions {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }

        .saved-filter-actions button {
            padding: 4px 8px;
            font-size: 12px;
            border: 1px solid #d9d9d9;
            border-radius: 3px;
            background-color: white;
            cursor: pointer;
            transition: all 0.2s;
        }

        .saved-filter-actions button:hover {
            background-color: #f0f0f0;
        }

        .saved-filter-actions .delete-btn {
            color: #ff4d4f;
            border-color: #ff4d4f;
        }

        .saved-filter-actions .delete-btn:hover {
            background-color: #fff2f0;
        }

        .empty-filters {
            text-align: center;
            padding: 40px 20px;
            color: #999;
        }

        textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            font-family: inherit;
            resize: vertical;
            min-height: 80px;
        }

        textarea:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        /* 高级图表样式 */
        .advanced-charts-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
            padding: 15px 0;
            border-bottom: 1px solid #e6e9ed;
        }

        .advanced-charts-header h2 {
            color: #2c3e50;
            font-size: 20px;
            font-weight: 500;
            margin: 0;
        }

        .chart-controls {
            display: flex;
            gap: 15px;
            align-items: end;
            flex-wrap: wrap;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .control-group label {
            font-size: 12px;
            color: #666;
            font-weight: 500;
        }

        .control-group select {
            padding: 6px 10px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            min-width: 120px;
        }

        .advanced-chart-container {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 20px;
            height: 600px;
        }

        .chart-main {
            background-color: white;
            border-radius: 6px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
            display: flex;
            flex-direction: column;
        }

        .chart-main .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
        }

        .chart-main .chart-title {
            font-size: 16px;
            font-weight: 500;
            color: #2c3e50;
        }

        .chart-options {
            display: flex;
            gap: 8px;
        }

        .btn-sm {
            padding: 4px 8px;
            font-size: 12px;
            border: 1px solid #d9d9d9;
            border-radius: 3px;
            background-color: white;
            cursor: pointer;
            transition: all 0.2s;
        }

        .btn-sm:hover {
            background-color: #f0f0f0;
            border-color: #bfbfbf;
        }

        .chart-main .chart-content {
            flex: 1;
            padding: 20px;
            min-height: 0;
        }

        .chart-sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .chart-info,
        .chart-legend,
        .chart-drill-down {
            background-color: white;
            border-radius: 6px;
            padding: 15px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
        }

        .chart-info h4,
        .chart-legend h4,
        .chart-drill-down h4 {
            margin: 0 0 15px 0;
            font-size: 14px;
            font-weight: 500;
            color: #2c3e50;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .info-label {
            font-size: 12px;
            color: #666;
        }

        .info-value {
            font-size: 14px;
            font-weight: 500;
            color: #2c3e50;
        }

        .drill-down-path {
            margin-bottom: 15px;
            font-size: 12px;
            color: #666;
        }

        .drill-down-actions {
            display: flex;
            gap: 8px;
        }

        .chart-fullscreen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background-color: white;
            z-index: 2000;
            display: flex;
            flex-direction: column;
        }

        .chart-fullscreen .chart-header {
            padding: 20px;
            border-bottom: 1px solid #f0f0f0;
        }

        .chart-fullscreen .chart-content {
            flex: 1;
            padding: 20px;
        }

        /* 批量操作样式 */
        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 15px;
            background-color: white;
            border-radius: 6px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
        }

        .table-actions {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .selection-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .checkbox-container {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            font-size: 14px;
        }

        .checkbox-container input[type="checkbox"] {
            margin: 0;
        }

        .selected-count {
            font-size: 14px;
            color: #666;
        }

        .batch-actions {
            display: flex;
            gap: 10px;
        }

        .table-tools {
            display: flex;
            gap: 10px;
        }

        .btn-danger {
            background-color: #ff4d4f;
            color: white;
            border: 1px solid #ff4d4f;
        }

        .btn-danger:hover {
            background-color: #ff7875;
            border-color: #ff7875;
        }

        .checkbox-column {
            width: 40px;
            text-align: center;
        }

        .data-table tbody tr.selected {
            background-color: #e6f7ff;
        }

        .data-table tbody tr:hover {
            background-color: #f9f9f9;
        }

        .data-table tbody tr.selected:hover {
            background-color: #bae7ff;
        }

        /* 批量编辑模态框样式 */
        .batch-edit-info {
            padding: 15px;
            background-color: #f0f8ff;
            border-radius: 4px;
            margin-bottom: 20px;
            border-left: 4px solid #1890ff;
        }

        .batch-edit-info p {
            margin: 0;
            color: #2c3e50;
        }

        .batch-edit-fields {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .field-group {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .field-group label {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            color: #2c3e50;
        }

        .field-group input[type="checkbox"] {
            margin: 0;
        }

        .field-group select,
        .field-group input[type="number"] {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
        }

        .field-group select:disabled,
        .field-group input:disabled {
            background-color: #f5f5f5;
            color: #999;
            cursor: not-allowed;
        }

        .price-adjustment {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .price-adjustment select {
            flex: 1;
        }

        .price-adjustment input {
            flex: 1;
        }

        .adjustment-unit {
            font-size: 14px;
            color: #666;
            min-width: 20px;
        }

        /* 数据验证结果样式 */
        .validation-results {
            background-color: white;
            border-radius: 6px;
            padding: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }

        .validation-summary {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #f0f0f0;
        }

        .validation-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            margin-bottom: 8px;
            border-radius: 4px;
            font-size: 14px;
        }

        .validation-item.error {
            background-color: #fff2f0;
            border-left: 4px solid #ff4d4f;
            color: #a8071a;
        }

        .validation-item.warning {
            background-color: #fffbe6;
            border-left: 4px solid #faad14;
            color: #d46b08;
        }

        .validation-item.success {
            background-color: #f6ffed;
            border-left: 4px solid #52c41a;
            color: #389e0d;
        }

        .validation-count {
            font-weight: 500;
            margin-left: 10px;
        }

        /* 数据备份和恢复样式 */
        .backup-options,
        .restore-options {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .backup-option,
        .restore-option {
            padding: 20px;
            border: 1px solid #e6e9ed;
            border-radius: 6px;
            background-color: #fafafa;
        }

        .backup-option h4,
        .restore-option h4 {
            margin: 0 0 15px 0;
            color: #2c3e50;
            font-size: 16px;
            font-weight: 500;
        }

        .backup-info {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin: 15px 0;
            padding: 15px;
            background-color: white;
            border-radius: 4px;
            border: 1px solid #e6e9ed;
        }

        .backup-info .info-item {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
        }

        .auto-backup-settings {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .auto-backup-config {
            padding: 15px;
            background-color: white;
            border-radius: 4px;
            border: 1px solid #e6e9ed;
        }

        .backup-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #e6e9ed;
            border-radius: 4px;
            background-color: white;
        }

        .backup-item {
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .backup-item:last-child {
            border-bottom: none;
        }

        .backup-item:hover {
            background-color: #f9f9f9;
        }

        .backup-item.selected {
            background-color: #e6f7ff;
            border-color: #1890ff;
        }

        .backup-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .backup-name {
            font-weight: 500;
            color: #2c3e50;
        }

        .backup-date {
            font-size: 12px;
            color: #666;
        }

        .backup-description {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
        }

        .backup-meta {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #999;
        }

        .backup-actions {
            display: flex;
            gap: 8px;
            margin-top: 10px;
        }

        .backup-actions button {
            padding: 4px 8px;
            font-size: 12px;
            border: 1px solid #d9d9d9;
            border-radius: 3px;
            background-color: white;
            cursor: pointer;
            transition: all 0.2s;
        }

        .backup-actions button:hover {
            background-color: #f0f0f0;
        }

        .backup-actions .delete-btn {
            color: #ff4d4f;
            border-color: #ff4d4f;
        }

        .backup-actions .delete-btn:hover {
            background-color: #fff2f0;
        }

        .file-restore {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .file-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            background-color: white;
            border: 1px solid #e6e9ed;
            border-radius: 4px;
        }

        .file-name {
            font-weight: 500;
            color: #2c3e50;
        }

        .file-size {
            font-size: 12px;
            color: #666;
        }

        .restore-warning {
            margin-top: 20px;
        }

        .warning-box {
            padding: 15px;
            background-color: #fff7e6;
            border: 1px solid #ffc53d;
            border-radius: 4px;
            border-left: 4px solid #ffc53d;
        }

        .warning-box strong {
            color: #d46b08;
        }

        .warning-box p {
            margin: 8px 0 0 0;
            color: #8c4a00;
            font-size: 14px;
        }

        .empty-backups {
            text-align: center;
            padding: 40px 20px;
            color: #999;
        }

        /* 智能分析样式 */
        .analytics-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px 0;
            border-bottom: 1px solid #e6e9ed;
        }

        .analytics-header h2 {
            color: #2c3e50;
            font-size: 20px;
            font-weight: 500;
            margin: 0;
        }

        .analytics-controls {
            display: flex;
            gap: 10px;
        }

        .analytics-container {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 20px;
            height: 600px;
        }

        .analytics-sidebar {
            background-color: white;
            border-radius: 6px;
            padding: 20px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
            overflow-y: auto;
        }

        .analysis-options h3,
        .analysis-params h4 {
            margin: 0 0 15px 0;
            color: #2c3e50;
            font-size: 16px;
            font-weight: 500;
        }

        .option-group {
            display: flex;
            flex-direction: column;
            gap: 12px;
            margin-bottom: 25px;
        }

        .analysis-params {
            border-top: 1px solid #f0f0f0;
            padding-top: 20px;
        }

        .param-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-bottom: 15px;
        }

        .param-group label {
            font-size: 14px;
            color: #666;
            font-weight: 500;
        }

        .param-group select {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
        }

        .analytics-main {
            background-color: white;
            border-radius: 6px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }

        .analysis-results {
            height: 100%;
            overflow-y: auto;
        }

        .analysis-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #999;
        }

        .placeholder-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }

        .analysis-placeholder h3 {
            margin: 0 0 10px 0;
            color: #666;
        }

        .analysis-placeholder p {
            margin: 0;
            color: #999;
        }

        /* 分析结果样式 */
        .analysis-section {
            padding: 20px;
            border-bottom: 1px solid #f0f0f0;
        }

        .analysis-section:last-child {
            border-bottom: none;
        }

        .analysis-section h3 {
            margin: 0 0 15px 0;
            color: #2c3e50;
            font-size: 18px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-icon {
            font-size: 20px;
        }

        .insights-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .insight-card {
            padding: 15px;
            border: 1px solid #e6e9ed;
            border-radius: 6px;
            background-color: #fafafa;
        }

        .insight-card.positive {
            border-left: 4px solid #52c41a;
            background-color: #f6ffed;
        }

        .insight-card.negative {
            border-left: 4px solid #ff4d4f;
            background-color: #fff2f0;
        }

        .insight-card.warning {
            border-left: 4px solid #faad14;
            background-color: #fffbe6;
        }

        .insight-card.info {
            border-left: 4px solid #1890ff;
            background-color: #e6f7ff;
        }

        .insight-title {
            font-weight: 500;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .insight-content {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
        }

        .insight-value {
            font-weight: 500;
            color: #2c3e50;
        }

        .predictions-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .prediction-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 15px;
            background-color: #f9f9f9;
            border-radius: 4px;
            border-left: 3px solid #1890ff;
        }

        .prediction-label {
            font-weight: 500;
            color: #2c3e50;
        }

        .prediction-value {
            font-size: 14px;
            color: #666;
        }

        .anomalies-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .anomaly-item {
            padding: 12px 15px;
            background-color: #fff2f0;
            border-radius: 4px;
            border-left: 3px solid #ff4d4f;
        }

        .anomaly-title {
            font-weight: 500;
            color: #a8071a;
            margin-bottom: 5px;
        }

        .anomaly-description {
            font-size: 14px;
            color: #8c4a00;
        }

        .recommendations-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .recommendation-item {
            padding: 15px;
            background-color: #e6f7ff;
            border-radius: 6px;
            border-left: 4px solid #1890ff;
        }

        .recommendation-title {
            font-weight: 500;
            color: #096dd9;
            margin-bottom: 8px;
        }

        .recommendation-content {
            font-size: 14px;
            color: #2c3e50;
            line-height: 1.5;
        }

        .recommendation-impact {
            margin-top: 8px;
            font-size: 12px;
            color: #52c41a;
            font-weight: 500;
        }
        
        /* 响应式设计 */
        @media (max-width: 1200px) {
            .container {
                padding: 15px;
            }

            .products-grid {
                grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            }
        }

        @media (max-width: 992px) {
            .dashboard {
                grid-template-columns: repeat(2, 1fr);
            }

            .chart-container {
                grid-template-columns: 1fr;
            }

            .products-grid {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            }

            .customers-stats {
                flex-direction: column;
                gap: 15px;
            }

            .customers-filters,
            .products-filters {
                flex-direction: column;
                gap: 10px;
            }

            .form-row {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .header .actions {
                justify-content: center;
            }

            .dashboard {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .filters {
                flex-direction: column;
                gap: 10px;
            }

            .tabs {
                overflow-x: auto;
                white-space: nowrap;
                padding-bottom: 5px;
            }

            .tab-btn {
                flex-shrink: 0;
                min-width: 80px;
            }

            .products-grid {
                grid-template-columns: 1fr;
            }

            .chart {
                height: 300px;
            }

            .today-header {
                flex-direction: column;
                gap: 10px;
                align-items: stretch;
            }

            .customers-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .products-header {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .customers-table-container,
            .table-container {
                overflow-x: auto;
            }

            .customers-table,
            .data-table {
                min-width: 600px;
            }

            .modal-content {
                width: 95%;
                margin: 20px;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 8px;
            }

            .header h1 {
                font-size: 20px;
            }

            .btn {
                padding: 6px 12px;
                font-size: 12px;
            }

            .card-value {
                font-size: 20px;
            }

            .chart {
                height: 250px;
            }

            .product-card {
                padding: 15px;
            }

            .pagination {
                flex-wrap: wrap;
                gap: 5px;
            }

            .page-btn {
                min-width: 35px;
                padding: 5px 8px;
                font-size: 12px;
            }
        }
