<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>利润分析数据工具</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <!-- 全局通知容器 -->
        <div class="notification-container" id="notification-container"></div>

        <div class="header">
            <h1>利润分析数据工具</h1>
            <div class="actions">
                <input type="file" id="import-file" accept=".csv,.xlsx,.xls" style="display: none;">
                <button class="btn btn-outline" id="import-btn">导入数据</button>
                <button class="btn btn-outline" id="export-btn">导出数据</button>
                <button class="btn btn-primary" id="refresh-btn">刷新数据</button>
            </div>
        </div>
        
        <div class="tabs">
            <button class="tab-btn active" data-tab="overview">总览</button>
            <button class="tab-btn" data-tab="today">今日数据</button>
            <button class="tab-btn" data-tab="trend">趋势分析</button>
            <button class="tab-btn" data-tab="category">类别分布</button>
            <button class="tab-btn" data-tab="advanced-charts">高级图表</button>
            <button class="tab-btn" data-tab="analytics">智能分析</button>
            <button class="tab-btn" data-tab="products">产品管理</button>
            <button class="tab-btn" data-tab="customers">客户管理</button>
            <button class="tab-btn" data-tab="table">明细表格</button>
        </div>
        
        <div class="tab-content" id="tab-overview" style="display: block;">
            <div class="filters">
                <div class="filter-item">
                    <label class="filter-label">日期范围</label>
                    <input type="date" class="filter-input" id="date-start" value="2023-01-01">
                    <span style="text-align: center; margin: 5px 0;">至</span>
                    <input type="date" class="filter-input" id="date-end" value="2023-12-31">
                </div>
                <div class="filter-item">
                    <label class="filter-label">产品类别</label>
                    <select class="filter-input" id="category-select">
                        <option value="all">全部</option>
                        <option value="default">默认</option>
                        <option value="电子产品">电子产品</option>
                        <option value="家居用品">家居用品</option>
                        <option value="服装鞋帽">服装鞋帽</option>
                    </select>
                </div>
                <div class="filter-item">
                    <label class="filter-label">销售区域</label>
                    <select class="filter-input" id="region-select">
                        <option value="all">全部地区</option>
                        <option value="北美">北美</option>
                        <option value="欧洲">欧洲</option>
                        <option value="亚洲">亚洲</option>
                        <option value="东南亚">东南亚</option>
                        <option value="中东">中东</option>
                        <option value="澳洲">澳洲</option>
                        <option value="华北地区">华北地区</option>
                        <option value="华东地区">华东地区</option>
                        <option value="华南地区">华南地区</option>
                        <option value="西部地区">西部地区</option>
                    </select>
                </div>
                <div class="filter-item">
                    <label class="filter-label">销售渠道</label>
                    <select class="filter-input" id="channel-select">
                        <option value="all">全部渠道</option>
                        <option value="跨境电商">跨境电商</option>
                        <option value="本土电商">本土电商</option>
                        <option value="线上渠道">线上渠道</option>
                        <option value="线下渠道">线下渠道</option>
                    </select>
                </div>
                <div class="filter-item">
                    <label class="filter-label">电商平台</label>
                    <select class="filter-input" id="platform-select">
                        <option value="all">全部</option>
                        <option value="Amazon">Amazon</option>
                        <option value="eBay">eBay</option>
                        <option value="Shopee">Shopee</option>
                        <option value="Lazada">Lazada</option>
                        <option value="AliExpress">AliExpress</option>
                        <option value="Walmart">Walmart</option>
                        <option value="Etsy">Etsy</option>
                        <option value="Shopify">Shopify</option>
                        <option value="自营">自营</option>
                    </select>
                </div>
                <div class="filter-item">
                    <label class="filter-label">查询</label>
                    <button class="btn btn-primary" style="margin-top: 20px;" id="query-btn">确定</button>
                </div>
                <div class="filter-item">
                    <label class="filter-label">高级功能</label>
                    <div style="margin-top: 20px; display: flex; gap: 10px; flex-wrap: wrap;">
                        <button class="btn btn-outline" id="advanced-filter-btn">高级筛选</button>
                        <button class="btn btn-outline" id="save-filter-btn">保存筛选</button>
                        <button class="btn btn-outline" id="load-filter-btn">加载筛选</button>
                    </div>
                </div>
            </div>
            <div class="dashboard">
                <div class="card">
                    <div class="card-title">总收入 (元)</div>
                    <div class="card-value">0</div>
                    <div class="card-trend trend-up"></div>
                </div>
                <div class="card">
                    <div class="card-title">总成本 (元)</div>
                    <div class="card-value">0</div>
                    <div class="card-trend trend-up"></div>
                </div>
                <div class="card">
                    <div class="card-title">净利润 (元)</div>
                    <div class="card-value">0</div>
                    <div class="card-trend trend-up"></div>
                </div>
                <div class="card">
                    <div class="card-title">净利润率</div>
                    <div class="card-value">0%</div>
                    <div class="card-trend trend-up"></div>
                </div>
            </div>
        </div>
        <div class="tab-content" id="tab-today" style="display: none;">
            <div class="today-header">
                <h2>今日数据概览</h2>
                <div class="today-date" id="today-date"></div>
            </div>

            <div class="dashboard">
                <div class="card">
                    <div class="card-title">今日销售额 (元)</div>
                    <div class="card-value" id="today-revenue">0</div>
                    <div class="card-trend trend-up" id="today-revenue-trend">+0%</div>
                </div>
                <div class="card">
                    <div class="card-title">今日订单数</div>
                    <div class="card-value" id="today-orders">0</div>
                    <div class="card-trend trend-up" id="today-orders-trend">+0</div>
                </div>
                <div class="card">
                    <div class="card-title">今日利润 (元)</div>
                    <div class="card-value" id="today-profit">0</div>
                    <div class="card-trend trend-up" id="today-profit-trend">+0%</div>
                </div>
                <div class="card">
                    <div class="card-title">今日利润率</div>
                    <div class="card-value" id="today-margin">0%</div>
                    <div class="card-trend trend-up" id="today-margin-trend">+0%</div>
                </div>
            </div>

            <div class="chart-container">
                <div class="chart">
                    <div class="chart-header">
                        <div class="chart-title">今日各平台销售情况</div>
                    </div>
                    <div class="chart-content" id="today-platform-chart">
                        <!-- 今日平台销售图表 -->
                    </div>
                </div>
                <div class="chart">
                    <div class="chart-header">
                        <div class="chart-title">今日热销产品TOP5</div>
                    </div>
                    <div class="chart-content" id="today-products-chart">
                        <!-- 今日热销产品图表 -->
                    </div>
                </div>
            </div>

            <div class="today-alerts">
                <h3>今日提醒</h3>
                <div class="alert-list" id="today-alerts">
                    <!-- 动态生成提醒内容 -->
                </div>
            </div>
        </div>
        <div class="tab-content" id="tab-trend" style="display: none;">
            <div class="chart-container">
                <div class="chart">
                    <div class="chart-header">
                        <div class="chart-title">月度收入、成本与利润趋势</div>
                        <div class="chart-legend">
                            <div class="legend-item">
                                <div class="legend-color legend-revenue"></div>
                                <span>收入</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color legend-cost"></div>
                                <span>成本</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color legend-profit"></div>
                                <span>利润</span>
                            </div>
                        </div>
                    </div>
                    <div class="chart-content" id="trend-chart">
                        <!-- 趋势图表容器 -->
                    </div>
                </div>
                <div class="chart">
                    <div class="chart-header">
                        <div class="chart-title">平台收入对比</div>
                    </div>
                    <div class="chart-content" id="platform-chart">
                        <!-- 平台对比图表容器 -->
                    </div>
                </div>
            </div>
        </div>
        <div class="tab-content" id="tab-category" style="display: none;">
            <div class="chart-container">
                <div class="chart">
                    <div class="chart-header">
                        <div class="chart-title">产品类别利润分布</div>
                    </div>
                    <div class="chart-content" id="category-pie-chart">
                        <!-- 类别饼图容器 -->
                    </div>
                </div>
                <div class="chart">
                    <div class="chart-header">
                        <div class="chart-title">区域销售分析</div>
                    </div>
                    <div class="chart-content" id="region-chart">
                        <!-- 区域分析图表容器 -->
                    </div>
                </div>
            </div>
        </div>
        <div class="tab-content" id="tab-advanced-charts" style="display: none;">
            <div class="advanced-charts-header">
                <h2>高级数据可视化</h2>
                <div class="chart-controls">
                    <div class="control-group">
                        <label>图表类型</label>
                        <select id="chart-type-selector">
                            <option value="heatmap">热力图</option>
                            <option value="scatter">散点图</option>
                            <option value="radar">雷达图</option>
                            <option value="sankey">桑基图</option>
                            <option value="treemap">树状图</option>
                            <option value="funnel">漏斗图</option>
                        </select>
                    </div>
                    <div class="control-group">
                        <label>时间范围</label>
                        <select id="time-range-selector">
                            <option value="7">最近7天</option>
                            <option value="30">最近30天</option>
                            <option value="90">最近90天</option>
                            <option value="all">全部时间</option>
                        </select>
                    </div>
                    <div class="control-group">
                        <label>数据维度</label>
                        <select id="dimension-selector">
                            <option value="category">按类别</option>
                            <option value="platform">按平台</option>
                            <option value="region">按区域</option>
                            <option value="month">按月份</option>
                        </select>
                    </div>
                    <button class="btn btn-primary" id="update-chart-btn">更新图表</button>
                    <button class="btn btn-outline" id="export-chart-btn">导出图表</button>
                </div>
            </div>

            <div class="advanced-chart-container">
                <div class="chart-main">
                    <div class="chart-header">
                        <div class="chart-title" id="advanced-chart-title">数据可视化</div>
                        <div class="chart-options">
                            <button class="btn btn-sm" id="fullscreen-btn">全屏</button>
                            <button class="btn btn-sm" id="download-btn">下载</button>
                            <button class="btn btn-sm" id="config-btn">配置</button>
                        </div>
                    </div>
                    <div class="chart-content" id="advanced-chart-content">
                        <!-- 高级图表内容 -->
                    </div>
                </div>

                <div class="chart-sidebar">
                    <div class="chart-info">
                        <h4>图表信息</h4>
                        <div class="info-item">
                            <span class="info-label">数据点数量</span>
                            <span class="info-value" id="data-points-count">0</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">最大值</span>
                            <span class="info-value" id="max-value">0</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">最小值</span>
                            <span class="info-value" id="min-value">0</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">平均值</span>
                            <span class="info-value" id="avg-value">0</span>
                        </div>
                    </div>

                    <div class="chart-legend" id="advanced-chart-legend">
                        <!-- 图表图例 -->
                    </div>

                    <div class="chart-drill-down" id="chart-drill-down">
                        <h4>数据钻取</h4>
                        <div class="drill-down-path" id="drill-down-path">
                            <!-- 钻取路径 -->
                        </div>
                        <div class="drill-down-actions">
                            <button class="btn btn-sm" id="drill-up-btn">返回上级</button>
                            <button class="btn btn-sm" id="reset-drill-btn">重置</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="tab-content" id="tab-analytics" style="display: none;">
            <div class="analytics-header">
                <h2>智能分析</h2>
                <div class="analytics-controls">
                    <button class="btn btn-primary" id="run-analysis-btn">运行分析</button>
                    <button class="btn btn-outline" id="export-analysis-btn">导出报告</button>
                </div>
            </div>

            <div class="analytics-container">
                <div class="analytics-sidebar">
                    <div class="analysis-options">
                        <h3>分析选项</h3>
                        <div class="option-group">
                            <label class="checkbox-container">
                                <input type="checkbox" id="trend-prediction" checked>
                                <span class="checkmark"></span>
                                趋势预测
                            </label>
                            <label class="checkbox-container">
                                <input type="checkbox" id="anomaly-detection" checked>
                                <span class="checkmark"></span>
                                异常检测
                            </label>
                            <label class="checkbox-container">
                                <input type="checkbox" id="profit-optimization" checked>
                                <span class="checkmark"></span>
                                利润优化
                            </label>
                            <label class="checkbox-container">
                                <input type="checkbox" id="market-insights" checked>
                                <span class="checkmark"></span>
                                市场洞察
                            </label>
                        </div>

                        <div class="analysis-params">
                            <h4>分析参数</h4>
                            <div class="param-group">
                                <label>预测周期</label>
                                <select id="prediction-period">
                                    <option value="7">7天</option>
                                    <option value="30" selected>30天</option>
                                    <option value="90">90天</option>
                                </select>
                            </div>
                            <div class="param-group">
                                <label>置信度</label>
                                <select id="confidence-level">
                                    <option value="0.8">80%</option>
                                    <option value="0.9" selected>90%</option>
                                    <option value="0.95">95%</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="analytics-main">
                    <div class="analysis-results" id="analysis-results">
                        <div class="analysis-placeholder">
                            <div class="placeholder-icon">📊</div>
                            <h3>智能分析</h3>
                            <p>点击"运行分析"开始智能数据分析</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="tab-content" id="tab-products" style="display: none;">
            <div class="products-header">
                <h2>产品管理</h2>
                <button class="btn btn-primary" id="add-product-btn">添加产品</button>
            </div>

            <div class="products-filters">
                <div class="filter-item">
                    <label class="filter-label">搜索产品</label>
                    <input type="text" class="filter-input" id="product-search" placeholder="输入产品名称...">
                </div>
                <div class="filter-item">
                    <label class="filter-label">产品类别</label>
                    <select class="filter-input" id="product-category-filter">
                        <option value="all">全部类别</option>
                        <option value="Electronics">电子产品</option>
                        <option value="Home & Garden">家居用品</option>
                        <option value="Fashion">服装鞋帽</option>
                        <option value="Sports & Outdoors">运动户外</option>
                    </select>
                </div>
                <div class="filter-item">
                    <label class="filter-label">销售平台</label>
                    <select class="filter-input" id="product-platform-filter">
                        <option value="all">全部平台</option>
                        <option value="Amazon">Amazon</option>
                        <option value="eBay">eBay</option>
                        <option value="Shopee">Shopee</option>
                        <option value="Lazada">Lazada</option>
                        <option value="AliExpress">AliExpress</option>
                    </select>
                </div>
                <button class="btn btn-outline" id="product-filter-btn">筛选</button>
            </div>

            <div class="products-grid" id="products-grid">
                <!-- 产品卡片将通过JavaScript动态生成 -->
            </div>

            <div class="pagination" id="products-pagination">
                <!-- 分页控件 -->
            </div>
        </div>

        <!-- 产品编辑模态框 -->
        <div class="modal" id="product-modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="modal-title">添加产品</h3>
                    <span class="modal-close" id="modal-close">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="product-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="product-name">产品名称 *</label>
                                <input type="text" id="product-name" name="name" required
                                       minlength="2" maxlength="100"
                                       placeholder="请输入产品名称">
                                <div class="error-message" id="product-name-error"></div>
                            </div>
                            <div class="form-group">
                                <label for="product-category">产品类别 *</label>
                                <select id="product-category" name="category" required>
                                    <option value="">请选择类别</option>
                                    <option value="Electronics">电子产品</option>
                                    <option value="Home & Garden">家居用品</option>
                                    <option value="Fashion">服装鞋帽</option>
                                    <option value="Sports & Outdoors">运动户外</option>
                                </select>
                                <div class="error-message" id="product-category-error"></div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="product-price">销售价格 (元) *</label>
                                <input type="number" id="product-price" name="price"
                                       step="0.01" min="0.01" max="999999" required
                                       placeholder="0.00">
                                <div class="error-message" id="product-price-error"></div>
                            </div>
                            <div class="form-group">
                                <label for="product-cost">采购成本 (元) *</label>
                                <input type="number" id="product-cost" name="cost"
                                       step="0.01" min="0" max="999999" required
                                       placeholder="0.00">
                                <div class="error-message" id="product-cost-error"></div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="product-platform">销售平台 *</label>
                                <select id="product-platform" name="platform" required>
                                    <option value="">请选择平台</option>
                                    <option value="Amazon">Amazon</option>
                                    <option value="eBay">eBay</option>
                                    <option value="Shopee">Shopee</option>
                                    <option value="Lazada">Lazada</option>
                                    <option value="AliExpress">AliExpress</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="product-region">销售区域 *</label>
                                <select id="product-region" name="region" required>
                                    <option value="">请选择区域</option>
                                    <option value="北美">北美</option>
                                    <option value="欧洲">欧洲</option>
                                    <option value="亚洲">亚洲</option>
                                    <option value="东南亚">东南亚</option>
                                    <option value="华东地区">华东地区</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="product-quantity">销售数量</label>
                                <input type="number" id="product-quantity" name="quantity" min="1" value="1">
                            </div>
                            <div class="form-group">
                                <label for="product-adspend">广告花费 (元)</label>
                                <input type="number" id="product-adspend" name="adSpend" step="0.01" min="0" value="0">
                            </div>
                        </div>
                        <div class="form-actions">
                            <button type="button" class="btn btn-outline" id="cancel-btn">取消</button>
                            <button type="submit" class="btn btn-primary">保存</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="tab-content" id="tab-customers" style="display: none;">
            <div class="customers-header">
                <h2>客户管理</h2>
                <div class="customers-stats">
                    <div class="stat-item">
                        <span class="stat-label">总客户数</span>
                        <span class="stat-value" id="total-customers">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">活跃客户</span>
                        <span class="stat-value" id="active-customers">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">平均订单价值</span>
                        <span class="stat-value" id="avg-order-value">¥0</span>
                    </div>
                </div>
            </div>

            <div class="customers-filters">
                <div class="filter-item">
                    <label class="filter-label">搜索客户</label>
                    <input type="text" class="filter-input" id="customer-search" placeholder="输入客户ID或区域...">
                </div>
                <div class="filter-item">
                    <label class="filter-label">客户区域</label>
                    <select class="filter-input" id="customer-region-filter">
                        <option value="all">全部区域</option>
                        <option value="北美">北美</option>
                        <option value="欧洲">欧洲</option>
                        <option value="亚洲">亚洲</option>
                        <option value="东南亚">东南亚</option>
                        <option value="华东地区">华东地区</option>
                    </select>
                </div>
                <div class="filter-item">
                    <label class="filter-label">客户价值</label>
                    <select class="filter-input" id="customer-value-filter">
                        <option value="all">全部客户</option>
                        <option value="high">高价值客户</option>
                        <option value="medium">中等价值客户</option>
                        <option value="low">低价值客户</option>
                    </select>
                </div>
                <button class="btn btn-outline" id="customer-filter-btn">筛选</button>
            </div>

            <div class="chart-container">
                <div class="chart">
                    <div class="chart-header">
                        <div class="chart-title">客户区域分布</div>
                    </div>
                    <div class="chart-content" id="customer-region-chart">
                        <!-- 客户区域分布图表 -->
                    </div>
                </div>
                <div class="chart">
                    <div class="chart-header">
                        <div class="chart-title">客户价值分析</div>
                    </div>
                    <div class="chart-content" id="customer-value-chart">
                        <!-- 客户价值分析图表 -->
                    </div>
                </div>
            </div>

            <div class="customers-table-container">
                <table class="customers-table">
                    <thead>
                        <tr>
                            <th>客户ID</th>
                            <th>区域</th>
                            <th>订单数量</th>
                            <th>总消费金额</th>
                            <th>平均订单价值</th>
                            <th>最近购买时间</th>
                            <th>客户价值等级</th>
                            <th>主要购买平台</th>
                        </tr>
                    </thead>
                    <tbody id="customers-table-body">
                        <!-- 客户数据将通过JavaScript动态生成 -->
                    </tbody>
                </table>
            </div>

            <div class="pagination" id="customers-pagination">
                <!-- 客户分页控件 -->
            </div>
        </div>

        <!-- 高级筛选模态框 -->
        <div class="modal" id="advanced-filter-modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>高级筛选</h3>
                    <span class="modal-close" id="filter-modal-close">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="advanced-filter-container">
                        <div class="filter-group">
                            <h4>数值范围筛选</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label>收入范围 (元)</label>
                                    <div class="range-inputs">
                                        <input type="number" id="revenue-min" placeholder="最小值" min="0">
                                        <span>至</span>
                                        <input type="number" id="revenue-max" placeholder="最大值" min="0">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label>利润范围 (元)</label>
                                    <div class="range-inputs">
                                        <input type="number" id="profit-min" placeholder="最小值">
                                        <span>至</span>
                                        <input type="number" id="profit-max" placeholder="最大值">
                                    </div>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label>利润率范围 (%)</label>
                                    <div class="range-inputs">
                                        <input type="number" id="margin-min" placeholder="最小值" min="0" max="100">
                                        <span>至</span>
                                        <input type="number" id="margin-max" placeholder="最大值" min="0" max="100">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label>销售数量范围</label>
                                    <div class="range-inputs">
                                        <input type="number" id="quantity-min" placeholder="最小值" min="0">
                                        <span>至</span>
                                        <input type="number" id="quantity-max" placeholder="最大值" min="0">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="filter-group">
                            <h4>多选筛选</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label>产品类别</label>
                                    <div class="checkbox-group" id="category-checkboxes">
                                        <label><input type="checkbox" value="Electronics"> 电子产品</label>
                                        <label><input type="checkbox" value="Home & Garden"> 家居用品</label>
                                        <label><input type="checkbox" value="Fashion"> 服装鞋帽</label>
                                        <label><input type="checkbox" value="Sports & Outdoors"> 运动户外</label>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label>销售平台</label>
                                    <div class="checkbox-group" id="platform-checkboxes">
                                        <label><input type="checkbox" value="Amazon"> Amazon</label>
                                        <label><input type="checkbox" value="eBay"> eBay</label>
                                        <label><input type="checkbox" value="Shopee"> Shopee</label>
                                        <label><input type="checkbox" value="Lazada"> Lazada</label>
                                        <label><input type="checkbox" value="AliExpress"> AliExpress</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="filter-group">
                            <h4>排序设置</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label>排序字段</label>
                                    <select id="sort-field">
                                        <option value="">请选择排序字段</option>
                                        <option value="revenue">收入</option>
                                        <option value="profit">利润</option>
                                        <option value="margin">利润率</option>
                                        <option value="quantity">销售数量</option>
                                        <option value="date">日期</option>
                                        <option value="name">产品名称</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>排序方向</label>
                                    <select id="sort-direction">
                                        <option value="desc">降序 (高到低)</option>
                                        <option value="asc">升序 (低到高)</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn btn-outline" id="reset-filter-btn">重置</button>
                        <button type="button" class="btn btn-outline" id="cancel-filter-btn">取消</button>
                        <button type="button" class="btn btn-primary" id="apply-filter-btn">应用筛选</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 保存筛选条件模态框 -->
        <div class="modal" id="save-filter-modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>保存筛选条件</h3>
                    <span class="modal-close" id="save-filter-modal-close">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="filter-name">筛选条件名称</label>
                        <input type="text" id="filter-name" placeholder="请输入筛选条件名称" maxlength="50">
                    </div>
                    <div class="form-group">
                        <label for="filter-description">描述 (可选)</label>
                        <textarea id="filter-description" placeholder="描述这个筛选条件的用途" rows="3"></textarea>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn btn-outline" id="cancel-save-filter-btn">取消</button>
                        <button type="button" class="btn btn-primary" id="confirm-save-filter-btn">保存</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 加载筛选条件模态框 -->
        <div class="modal" id="load-filter-modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>加载筛选条件</h3>
                    <span class="modal-close" id="load-filter-modal-close">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="saved-filters-list" id="saved-filters-list">
                        <!-- 保存的筛选条件列表将通过JavaScript动态生成 -->
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn btn-outline" id="cancel-load-filter-btn">取消</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="tab-content" id="tab-table" style="display: none;">
            <div class="table-header">
                <div class="table-actions">
                    <div class="selection-info">
                        <label class="checkbox-container">
                            <input type="checkbox" id="select-all-checkbox">
                            <span class="checkmark"></span>
                            全选
                        </label>
                        <span class="selected-count" id="selected-count">已选择 0 项</span>
                    </div>
                    <div class="batch-actions" id="batch-actions" style="display: none;">
                        <button class="btn btn-outline" id="batch-edit-btn">批量编辑</button>
                        <button class="btn btn-outline" id="batch-export-btn">批量导出</button>
                        <button class="btn btn-danger" id="batch-delete-btn">批量删除</button>
                    </div>
                </div>
                <div class="table-tools">
                    <button class="btn btn-outline" id="validate-data-btn">数据验证</button>
                    <button class="btn btn-outline" id="duplicate-check-btn">重复检查</button>
                    <button class="btn btn-outline" id="backup-data-btn">数据备份</button>
                    <button class="btn btn-outline" id="restore-data-btn">数据恢复</button>
                    <button class="btn btn-outline" id="bulk-import-btn">批量导入</button>
                </div>
            </div>

            <div class="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th class="checkbox-column">
                                <input type="checkbox" id="header-select-all">
                            </th>
                            <th data-column="name">产品名称</th>
                            <th data-column="category">类别</th>
                            <th data-column="region">区域</th>
                            <th data-column="platform">平台</th>
                            <th data-column="quantity">数量</th>
                            <th data-column="price">价格</th>
                            <th data-column="cost">成本</th>
                            <th data-column="revenue">收入</th>
                            <th data-column="profit">净利润</th>
                            <th data-column="margin">利润率</th>
                            <th data-column="date">日期</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="data-table-body">
                        <!-- 数据行将通过JavaScript动态生成 -->
                    </tbody>
                </table>
            </div>
            <div class="pagination" id="pagination">
                <!-- 分页控件将通过JavaScript动态生成 -->
            </div>
        </div>

        <!-- 批量编辑模态框 -->
        <div class="modal" id="batch-edit-modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>批量编辑</h3>
                    <span class="modal-close" id="batch-edit-modal-close">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="batch-edit-info">
                        <p>将对 <span id="batch-edit-count">0</span> 个项目进行批量编辑</p>
                    </div>

                    <div class="batch-edit-fields">
                        <div class="field-group">
                            <label>
                                <input type="checkbox" id="edit-category-check">
                                批量修改类别
                            </label>
                            <select id="batch-category" disabled>
                                <option value="">请选择类别</option>
                                <option value="Electronics">电子产品</option>
                                <option value="Home & Garden">家居用品</option>
                                <option value="Fashion">服装鞋帽</option>
                                <option value="Sports & Outdoors">运动户外</option>
                            </select>
                        </div>

                        <div class="field-group">
                            <label>
                                <input type="checkbox" id="edit-platform-check">
                                批量修改平台
                            </label>
                            <select id="batch-platform" disabled>
                                <option value="">请选择平台</option>
                                <option value="Amazon">Amazon</option>
                                <option value="eBay">eBay</option>
                                <option value="Shopee">Shopee</option>
                                <option value="Lazada">Lazada</option>
                                <option value="AliExpress">AliExpress</option>
                            </select>
                        </div>

                        <div class="field-group">
                            <label>
                                <input type="checkbox" id="edit-region-check">
                                批量修改区域
                            </label>
                            <select id="batch-region" disabled>
                                <option value="">请选择区域</option>
                                <option value="北美">北美</option>
                                <option value="欧洲">欧洲</option>
                                <option value="亚洲">亚洲</option>
                                <option value="东南亚">东南亚</option>
                                <option value="华东地区">华东地区</option>
                            </select>
                        </div>

                        <div class="field-group">
                            <label>
                                <input type="checkbox" id="edit-price-check">
                                批量调整价格
                            </label>
                            <div class="price-adjustment">
                                <select id="price-adjustment-type" disabled>
                                    <option value="percentage">按百分比</option>
                                    <option value="fixed">固定金额</option>
                                </select>
                                <input type="number" id="price-adjustment-value" placeholder="调整值" disabled>
                                <span class="adjustment-unit" id="adjustment-unit">%</span>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn btn-outline" id="cancel-batch-edit-btn">取消</button>
                        <button type="button" class="btn btn-primary" id="confirm-batch-edit-btn">确认修改</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据备份模态框 -->
        <div class="modal" id="backup-modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>数据备份</h3>
                    <span class="modal-close" id="backup-modal-close">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="backup-options">
                        <div class="backup-option">
                            <h4>创建新备份</h4>
                            <div class="form-group">
                                <label for="backup-name">备份名称</label>
                                <input type="text" id="backup-name" placeholder="输入备份名称" maxlength="50">
                            </div>
                            <div class="form-group">
                                <label for="backup-description">备份描述</label>
                                <textarea id="backup-description" placeholder="描述这个备份的内容和用途" rows="3"></textarea>
                            </div>
                            <div class="backup-info">
                                <div class="info-item">
                                    <span>当前数据量：</span>
                                    <span id="current-data-count">0</span> 条记录
                                </div>
                                <div class="info-item">
                                    <span>备份大小：</span>
                                    <span id="backup-size-estimate">约 0 KB</span>
                                </div>
                            </div>
                            <button class="btn btn-primary" id="create-backup-btn">创建备份</button>
                        </div>

                        <div class="backup-option">
                            <h4>自动备份设置</h4>
                            <div class="auto-backup-settings">
                                <label class="checkbox-container">
                                    <input type="checkbox" id="auto-backup-enabled">
                                    <span class="checkmark"></span>
                                    启用自动备份
                                </label>
                                <div class="auto-backup-config" id="auto-backup-config" style="display: none;">
                                    <div class="form-group">
                                        <label>备份频率</label>
                                        <select id="auto-backup-frequency">
                                            <option value="daily">每日</option>
                                            <option value="weekly">每周</option>
                                            <option value="monthly">每月</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>保留数量</label>
                                        <input type="number" id="auto-backup-keep" min="1" max="50" value="10">
                                    </div>
                                </div>
                            </div>
                            <button class="btn btn-outline" id="save-auto-backup-btn">保存设置</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据恢复模态框 -->
        <div class="modal" id="restore-modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>数据恢复</h3>
                    <span class="modal-close" id="restore-modal-close">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="restore-options">
                        <div class="restore-option">
                            <h4>从备份恢复</h4>
                            <div class="backup-list" id="backup-list">
                                <!-- 备份列表将通过JavaScript动态生成 -->
                            </div>
                        </div>

                        <div class="restore-option">
                            <h4>从文件恢复</h4>
                            <div class="file-restore">
                                <input type="file" id="restore-file" accept=".json,.csv" style="display: none;">
                                <button class="btn btn-outline" id="select-restore-file-btn">选择备份文件</button>
                                <div class="file-info" id="restore-file-info" style="display: none;">
                                    <span class="file-name" id="restore-file-name"></span>
                                    <span class="file-size" id="restore-file-size"></span>
                                </div>
                                <button class="btn btn-primary" id="restore-from-file-btn" style="display: none;">从文件恢复</button>
                            </div>
                        </div>
                    </div>

                    <div class="restore-warning">
                        <div class="warning-box">
                            <strong>⚠️ 重要提醒：</strong>
                            <p>恢复操作将替换当前所有数据，此操作不可撤销。建议在恢复前先创建当前数据的备份。</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入 ECharts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.3.3/dist/echarts.min.js"></script>
    <script src="script.js"></script>
</body>
</html>