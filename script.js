document.addEventListener('DOMContentLoaded', function () {
            // 通知系统
            class NotificationSystem {
                constructor() {
                    this.container = document.getElementById('notification-container');
                    this.notifications = [];
                }

                show(message, type = 'info', duration = 5000) {
                    const notification = this.createNotification(message, type);
                    this.container.appendChild(notification);
                    this.notifications.push(notification);

                    // 自动移除
                    if (duration > 0) {
                        setTimeout(() => {
                            this.remove(notification);
                        }, duration);
                    }

                    return notification;
                }

                createNotification(message, type) {
                    const notification = document.createElement('div');
                    notification.className = `notification ${type}`;

                    const icons = {
                        success: '✓',
                        error: '✗',
                        warning: '⚠',
                        info: 'ℹ'
                    };

                    notification.innerHTML = `
                        <div class="notification-content">
                            <span class="notification-icon">${icons[type] || icons.info}</span>
                            <span class="notification-message">${message}</span>
                        </div>
                        <span class="notification-close">&times;</span>
                    `;

                    // 添加关闭事件
                    notification.querySelector('.notification-close').addEventListener('click', () => {
                        this.remove(notification);
                    });

                    return notification;
                }

                remove(notification) {
                    if (notification && notification.parentNode) {
                        notification.style.animation = 'slideOut 0.3s ease-out';
                        setTimeout(() => {
                            if (notification.parentNode) {
                                notification.parentNode.removeChild(notification);
                            }
                            const index = this.notifications.indexOf(notification);
                            if (index > -1) {
                                this.notifications.splice(index, 1);
                            }
                        }, 300);
                    }
                }

                success(message, duration = 5000) {
                    return this.show(message, 'success', duration);
                }

                error(message, duration = 8000) {
                    return this.show(message, 'error', duration);
                }

                warning(message, duration = 6000) {
                    return this.show(message, 'warning', duration);
                }

                info(message, duration = 5000) {
                    return this.show(message, 'info', duration);
                }
            }

            // 表单验证系统
            class FormValidator {
                constructor() {
                    this.rules = {};
                    this.messages = {};
                }

                addRule(fieldId, rule, message) {
                    if (!this.rules[fieldId]) {
                        this.rules[fieldId] = [];
                        this.messages[fieldId] = [];
                    }
                    this.rules[fieldId].push(rule);
                    this.messages[fieldId].push(message);
                }

                validate(fieldId, value) {
                    const rules = this.rules[fieldId] || [];
                    const messages = this.messages[fieldId] || [];

                    for (let i = 0; i < rules.length; i++) {
                        if (!rules[i](value)) {
                            return { valid: false, message: messages[i] };
                        }
                    }
                    return { valid: true, message: '' };
                }

                validateForm(formId) {
                    const form = document.getElementById(formId);
                    if (!form) return { valid: false, errors: {} };

                    const errors = {};
                    let isValid = true;

                    // 验证所有注册的字段
                    Object.keys(this.rules).forEach(fieldId => {
                        const field = document.getElementById(fieldId);
                        if (field) {
                            const result = this.validate(fieldId, field.value);
                            if (!result.valid) {
                                errors[fieldId] = result.message;
                                isValid = false;
                                this.showFieldError(fieldId, result.message);
                            } else {
                                this.clearFieldError(fieldId);
                            }
                        }
                    });

                    return { valid: isValid, errors };
                }

                showFieldError(fieldId, message) {
                    const field = document.getElementById(fieldId);
                    const errorElement = document.getElementById(fieldId + '-error');
                    const formGroup = field?.closest('.form-group');

                    if (errorElement) {
                        errorElement.textContent = message;
                    }
                    if (formGroup) {
                        formGroup.classList.add('error');
                        formGroup.classList.remove('success');
                    }
                }

                clearFieldError(fieldId) {
                    const field = document.getElementById(fieldId);
                    const errorElement = document.getElementById(fieldId + '-error');
                    const formGroup = field?.closest('.form-group');

                    if (errorElement) {
                        errorElement.textContent = '';
                    }
                    if (formGroup) {
                        formGroup.classList.remove('error');
                        formGroup.classList.add('success');
                    }
                }
            }

            // 加载指示器
            class LoadingIndicator {
                constructor() {
                    this.overlay = null;
                }

                show(message = '处理中...') {
                    this.hide(); // 确保只有一个加载指示器

                    this.overlay = document.createElement('div');
                    this.overlay.className = 'loading-overlay';
                    this.overlay.innerHTML = `
                        <div style="text-align: center; color: white;">
                            <div class="loading-spinner"></div>
                            <div style="margin-top: 15px;">${message}</div>
                        </div>
                    `;
                    document.body.appendChild(this.overlay);
                }

                hide() {
                    if (this.overlay && this.overlay.parentNode) {
                        this.overlay.parentNode.removeChild(this.overlay);
                        this.overlay = null;
                    }
                }
            }

            // 初始化系统
            const notifications = new NotificationSystem();
            const validator = new FormValidator();
            const loading = new LoadingIndicator();

            // 高级筛选管理系统
            class AdvancedFilterManager {
                constructor() {
                    this.currentFilters = {};
                    this.savedFilters = this.loadSavedFilters();
                }

                // 保存筛选条件到本地存储
                saveFilter(name, description, filters) {
                    const filterId = Date.now().toString();
                    const filterData = {
                        id: filterId,
                        name: name,
                        description: description,
                        filters: filters,
                        createdAt: new Date().toISOString(),
                        usageCount: 0
                    };

                    this.savedFilters[filterId] = filterData;
                    this.saveSavedFilters();
                    return filterId;
                }

                // 删除保存的筛选条件
                deleteFilter(filterId) {
                    delete this.savedFilters[filterId];
                    this.saveSavedFilters();
                }

                // 加载筛选条件
                loadFilter(filterId) {
                    const filter = this.savedFilters[filterId];
                    if (filter) {
                        filter.usageCount++;
                        this.saveSavedFilters();
                        return filter.filters;
                    }
                    return null;
                }

                // 获取所有保存的筛选条件
                getSavedFilters() {
                    return Object.values(this.savedFilters).sort((a, b) =>
                        new Date(b.createdAt) - new Date(a.createdAt)
                    );
                }

                // 应用筛选条件
                applyFilters(data, filters) {
                    return data.filter(item => {
                        const metrics = calculateMetrics(item);

                        // 数值范围筛选
                        if (filters.revenueMin !== undefined && metrics.revenue < filters.revenueMin) return false;
                        if (filters.revenueMax !== undefined && metrics.revenue > filters.revenueMax) return false;
                        if (filters.profitMin !== undefined && metrics.netProfit < filters.profitMin) return false;
                        if (filters.profitMax !== undefined && metrics.netProfit > filters.profitMax) return false;
                        if (filters.marginMin !== undefined && (metrics.netProfitMargin * 100) < filters.marginMin) return false;
                        if (filters.marginMax !== undefined && (metrics.netProfitMargin * 100) > filters.marginMax) return false;
                        if (filters.quantityMin !== undefined && item.quantity < filters.quantityMin) return false;
                        if (filters.quantityMax !== undefined && item.quantity > filters.quantityMax) return false;

                        // 多选筛选
                        if (filters.categories && filters.categories.length > 0 && !filters.categories.includes(item.category)) return false;
                        if (filters.platforms && filters.platforms.length > 0 && !filters.platforms.includes(item.platform)) return false;

                        return true;
                    });
                }

                // 排序数据
                sortData(data, sortField, sortDirection) {
                    if (!sortField) return data;

                    return [...data].sort((a, b) => {
                        let valueA, valueB;

                        switch (sortField) {
                            case 'revenue':
                                valueA = calculateMetrics(a).revenue;
                                valueB = calculateMetrics(b).revenue;
                                break;
                            case 'profit':
                                valueA = calculateMetrics(a).netProfit;
                                valueB = calculateMetrics(b).netProfit;
                                break;
                            case 'margin':
                                valueA = calculateMetrics(a).netProfitMargin;
                                valueB = calculateMetrics(b).netProfitMargin;
                                break;
                            case 'quantity':
                                valueA = a.quantity;
                                valueB = b.quantity;
                                break;
                            case 'date':
                                valueA = new Date(a.date);
                                valueB = new Date(b.date);
                                break;
                            case 'name':
                                valueA = a.name.toLowerCase();
                                valueB = b.name.toLowerCase();
                                break;
                            default:
                                return 0;
                        }

                        if (typeof valueA === 'string') {
                            return sortDirection === 'asc' ? valueA.localeCompare(valueB) : valueB.localeCompare(valueA);
                        } else {
                            return sortDirection === 'asc' ? valueA - valueB : valueB - valueA;
                        }
                    });
                }

                // 从本地存储加载保存的筛选条件
                loadSavedFilters() {
                    try {
                        const saved = localStorage.getItem('profit_analysis_saved_filters');
                        return saved ? JSON.parse(saved) : {};
                    } catch (error) {
                        console.error('加载保存的筛选条件失败:', error);
                        return {};
                    }
                }

                // 保存筛选条件到本地存储
                saveSavedFilters() {
                    try {
                        localStorage.setItem('profit_analysis_saved_filters', JSON.stringify(this.savedFilters));
                    } catch (error) {
                        console.error('保存筛选条件失败:', error);
                        notifications.error('保存筛选条件失败，可能是存储空间不足');
                    }
                }
            }

            // 全局可用
            const filterManager = new AdvancedFilterManager();
            window.notifications = notifications;
            window.validator = validator;
            window.loading = loading;
            window.filterManager = filterManager;

            // 设置产品表单验证规则
            function setupProductFormValidation() {
                // 产品名称验证
                validator.addRule('product-name',
                    value => value && value.trim().length >= 2,
                    '产品名称至少需要2个字符');
                validator.addRule('product-name',
                    value => value && value.trim().length <= 100,
                    '产品名称不能超过100个字符');

                // 产品类别验证
                validator.addRule('product-category',
                    value => value && value.trim() !== '',
                    '请选择产品类别');

                // 价格验证
                validator.addRule('product-price',
                    value => value && !isNaN(value) && parseFloat(value) > 0,
                    '销售价格必须大于0');
                validator.addRule('product-price',
                    value => value && parseFloat(value) <= 999999,
                    '销售价格不能超过999,999元');

                // 成本验证
                validator.addRule('product-cost',
                    value => value !== '' && !isNaN(value) && parseFloat(value) >= 0,
                    '采购成本必须大于等于0');
                validator.addRule('product-cost',
                    value => value && parseFloat(value) <= 999999,
                    '采购成本不能超过999,999元');

                // 平台验证
                validator.addRule('product-platform',
                    value => value && value.trim() !== '',
                    '请选择销售平台');

                // 区域验证
                validator.addRule('product-region',
                    value => value && value.trim() !== '',
                    '请选择销售区域');

                // 数量验证
                validator.addRule('product-quantity',
                    value => value && !isNaN(value) && parseInt(value) > 0,
                    '销售数量必须大于0');

                // 广告费用验证
                validator.addRule('product-adspend',
                    value => value === '' || (!isNaN(value) && parseFloat(value) >= 0),
                    '广告花费必须大于等于0');

                // 添加实时验证
                const fields = ['product-name', 'product-category', 'product-price', 'product-cost',
                              'product-platform', 'product-region', 'product-quantity', 'product-adspend'];

                fields.forEach(fieldId => {
                    const field = document.getElementById(fieldId);
                    if (field) {
                        field.addEventListener('blur', () => {
                            const result = validator.validate(fieldId, field.value);
                            if (!result.valid) {
                                validator.showFieldError(fieldId, result.message);
                            } else {
                                validator.clearFieldError(fieldId);
                            }
                        });

                        field.addEventListener('input', () => {
                            // 清除错误状态，但不显示成功状态
                            const formGroup = field.closest('.form-group');
                            if (formGroup && formGroup.classList.contains('error')) {
                                const result = validator.validate(fieldId, field.value);
                                if (result.valid) {
                                    validator.clearFieldError(fieldId);
                                }
                            }
                        });
                    }
                });
            }

            // 数据完整性检查
            function validateDataIntegrity(data) {
                const errors = [];

                if (!Array.isArray(data)) {
                    errors.push('数据格式错误：必须是数组格式');
                    return { valid: false, errors };
                }

                data.forEach((item, index) => {
                    if (!item.name || typeof item.name !== 'string') {
                        errors.push(`第${index + 1}行：产品名称无效`);
                    }
                    if (!item.price || isNaN(item.price) || item.price <= 0) {
                        errors.push(`第${index + 1}行：价格无效`);
                    }
                    if (item.cost === undefined || isNaN(item.cost) || item.cost < 0) {
                        errors.push(`第${index + 1}行：成本无效`);
                    }
                    if (!item.category || typeof item.category !== 'string') {
                        errors.push(`第${index + 1}行：类别无效`);
                    }
                    if (!item.id || isNaN(item.id)) {
                        errors.push(`第${index + 1}行：ID无效`);
                    }
                    if (!item.date || !/^\d{4}-\d{2}-\d{2}$/.test(item.date)) {
                        errors.push(`第${index + 1}行：日期格式无效`);
                    }
                });

                // 检查ID唯一性
                const idSet = new Set();
                const nameSet = new Set();
                data.forEach((item, index) => {
                    if (idSet.has(item.id)) {
                        errors.push(`第${index + 1}行：ID ${item.id} 重复`);
                    } else {
                        idSet.add(item.id);
                    }

                    const normalizedName = item.name ? item.name.toLowerCase().trim() : '';
                    if (normalizedName && nameSet.has(normalizedName)) {
                        errors.push(`第${index + 1}行：产品名称 "${item.name}" 重复`);
                    } else if (normalizedName) {
                        nameSet.add(normalizedName);
                    }
                });

                return { valid: errors.length === 0, errors };
            }

            // 1. 数据层 (Data Layer) - 模拟跨境电商平台数据
            const allData = [
                // 今日数据 - 确保有当日数据显示
                { id: 1001, name: 'Smart Watch Pro 2025', category: 'Electronics', region: '北美', channel: '跨境电商', platform: 'Amazon', quantity: 156, price: 199.99, cost: 85, date: new Date().toISOString().slice(0, 10), sizeCategory: 'standard', adSpend: 800, returnRate: 0.03, exchangeRateLoss: 0.02, customsDutyRate: 0.05, crossBorderLogisticsFee: 3.5, paymentProcessingRate: 0.029, inboundPlacementFee: 0.27, lowInventoryFee: 0, storageUtilizationSurcharge: 0 },
                { id: 1002, name: 'Bluetooth Speaker Mini', category: 'Electronics', region: '欧洲', channel: '跨境电商', platform: 'Amazon', quantity: 89, price: 49.99, cost: 22, date: new Date().toISOString().slice(0, 10), sizeCategory: 'standard', adSpend: 300, returnRate: 0.04, exchangeRateLoss: 0.02, customsDutyRate: 0.05, crossBorderLogisticsFee: 3.5, paymentProcessingRate: 0.029, inboundPlacementFee: 0.27, lowInventoryFee: 0, storageUtilizationSurcharge: 0 },
                { id: 1003, name: 'Wireless Charging Pad', category: 'Electronics', region: '亚太', channel: '跨境电商', platform: 'Shopee', quantity: 234, price: 29.99, cost: 12, date: new Date().toISOString().slice(0, 10), sizeCategory: 'standard', adSpend: 450, returnRate: 0.02, exchangeRateLoss: 0.02, customsDutyRate: 0.05, crossBorderLogisticsFee: 3.5, paymentProcessingRate: 0.029, inboundPlacementFee: 0.27, lowInventoryFee: 0, storageUtilizationSurcharge: 0 },

                // Amazon 历史数据
                { id: 1, name: 'Wireless Earbuds Pro', category: 'Electronics', region: '北美', channel: '跨境电商', platform: 'Amazon', quantity: 1532, price: 79.99, cost: 35, date: '2024-03-15', sizeCategory: 'standard', adSpend: 2500, returnRate: 0.05, exchangeRateLoss: 0.02, customsDutyRate: 0.05, crossBorderLogisticsFee: 3.5, paymentProcessingRate: 0.029, inboundPlacementFee: 0.27, lowInventoryFee: 0, storageUtilizationSurcharge: 0 },
                { id: 2, name: 'Smart Watch Series X', category: 'Electronics', region: '欧洲', channel: '跨境电商', platform: 'Amazon', quantity: 857, price: 199.99, cost: 120, date: '2024-04-20', sizeCategory: 'standard', adSpend: 3200, returnRate: 0.03, exchangeRateLoss: 0.025, customsDutyRate: 0.08, crossBorderLogisticsFee: 4.2, paymentProcessingRate: 0.029, inboundPlacementFee: 0.27, lowInventoryFee: 0.15, storageUtilizationSurcharge: 0 },
                { id: 3, name: 'Bluetooth Speaker Mini', category: 'Electronics', region: '亚洲', channel: '跨境电商', platform: 'Amazon', quantity: 2134, price: 29.99, cost: 15, date: '2024-05-10', sizeCategory: 'standard', adSpend: 1800, returnRate: 0.02, exchangeRateLoss: 0.015, customsDutyRate: 0.03, crossBorderLogisticsFee: 2.8, paymentProcessingRate: 0.029, inboundPlacementFee: 0.27, lowInventoryFee: 0, storageUtilizationSurcharge: 0.25 },

                // eBay 数据
                { id: 4, name: 'Vintage Leather Bag', category: 'Fashion', region: '北美', channel: '跨境电商', platform: 'eBay', quantity: 325, price: 89.99, cost: 45, date: '2024-06-25', adSpend: 1500, returnRate: 0.04, exchangeRateLoss: 0.02, customsDutyRate: 0.12, crossBorderLogisticsFee: 5.5, paymentProcessingRate: 0.035, promotedListingsRate: 0.02 },
                { id: 5, name: 'Handmade Ceramic Mug', category: 'Home & Garden', region: '欧洲', channel: '跨境电商', platform: 'eBay', quantity: 418, price: 24.99, cost: 12, date: '2024-07-01', adSpend: 800, returnRate: 0.03, exchangeRateLoss: 0.025, customsDutyRate: 0.15, crossBorderLogisticsFee: 3.2, paymentProcessingRate: 0.035, promotedListingsRate: 0.015 },

                // Shopee 数据
                { id: 6, name: 'Phone Case Clear', category: 'Electronics', region: '东南亚', channel: '跨境电商', platform: 'Shopee', quantity: 3245, price: 8.99, cost: 3.5, date: '2024-08-18', adSpend: 1200, returnRate: 0.05, exchangeRateLoss: 0.01, customsDutyRate: 0.02, crossBorderLogisticsFee: 1.5, paymentProcessingRate: 0.025 },
                { id: 7, name: 'LED Strip Lights', category: 'Home & Garden', region: '东南亚', channel: '跨境电商', platform: 'Shopee', quantity: 1876, price: 15.99, cost: 8, date: '2024-09-30', adSpend: 900, returnRate: 0.04, exchangeRateLoss: 0.01, customsDutyRate: 0.03, crossBorderLogisticsFee: 2.1, paymentProcessingRate: 0.025 },

                // Lazada 数据
                { id: 8, name: 'Fitness Tracker Band', category: 'Sports & Outdoors', region: '东南亚', channel: '跨境电商', platform: 'Lazada', quantity: 980, price: 39.99, cost: 22, date: '2024-10-05', adSpend: 1500, returnRate: 0.02, exchangeRateLoss: 0.015, customsDutyRate: 0.05, crossBorderLogisticsFee: 2.8, paymentProcessingRate: 0.03, lazadaLogisticsFee: 2.0 },
                { id: 9, name: 'Yoga Mat Premium', category: 'Sports & Outdoors', region: '东南亚', channel: '跨境电商', platform: 'Lazada', quantity: 210, price: 49.99, cost: 25, date: '2024-11-11', adSpend: 800, returnRate: 0.03, exchangeRateLoss: 0.015, customsDutyRate: 0.08, crossBorderLogisticsFee: 4.5, paymentProcessingRate: 0.03, lazadaLogisticsFee: 3.0 },

                // AliExpress 数据
                { id: 10, name: 'USB Cable 3-Pack', category: 'Electronics', region: '欧洲', channel: '跨境电商', platform: 'AliExpress', quantity: 5500, price: 12.99, cost: 4.5, date: '2024-12-01', adSpend: 2000, returnRate: 0.06, exchangeRateLoss: 0.02, customsDutyRate: 0.04, crossBorderLogisticsFee: 1.8, paymentProcessingRate: 0.028, goldSupplier: true },
                { id: 11, name: 'Wireless Mouse Gaming', category: 'Electronics', region: '北美', channel: '跨境电商', platform: 'AliExpress', quantity: 1200, price: 25.99, cost: 12, date: '2024-12-15', adSpend: 1200, returnRate: 0.04, exchangeRateLoss: 0.02, customsDutyRate: 0.06, crossBorderLogisticsFee: 2.5, paymentProcessingRate: 0.028, goldSupplier: false },

                // 本土Amazon数据（对比用）
                { id: 12, name: '智能音箱', category: 'Electronics', region: '华东地区', channel: '本土电商', platform: 'Amazon', quantity: 800, price: 299, cost: 180, date: '2024-11-20', sizeCategory: 'standard', adSpend: 8000, returnRate: 0.03, exchangeRateLoss: 0, customsDutyRate: 0, crossBorderLogisticsFee: 0, paymentProcessingRate: 0.025, inboundPlacementFee: 0.27, lowInventoryFee: 0, storageUtilizationSurcharge: 0 }
            ];

            // 为数据添加默认值
            allData.forEach(item => {
                // 设置默认值
                item.removalFee = item.removalFee || 0;
                item.inboundShipping = item.inboundShipping || 0;
                item.taxes = item.taxes || 0;
                item.storageFee = item.storageFee || 1;
                item.longTermStorageFee = item.longTermStorageFee || 0;
                item.closingFee = item.closingFee || 0;
                item.fbaFee = item.fbaFee || (item.sizeCategory === 'standard' ? 3.5 : 8.5);
            });

            // ========== 平台特定费用计算规则 ==========

            // Amazon FBA 2024 referral fee rates by category
            const amazonReferralRates = {
                'Electronics': 0.08,           // 8%
                'Home & Garden': 0.15,         // 15%
                'Clothing & Accessories': 0.17, // 17% (reduced to 5% for items under $20)
                'Sports & Outdoors': 0.15,     // 15%
                'Books': 0.15,                 // 15%
                'Health & Personal Care': 0.15, // 15%
                'Beauty': 0.15,                // 15%
                'Toys & Games': 0.15,          // 15%
                'Automotive': 0.12,            // 12%
                'Industrial & Scientific': 0.12, // 12%
                'default': 0.15                // Default 15%
            };

            // Cross-border ecommerce platform fee structures
            const platformFeeStructures = {
                'Amazon': {
                    referralFeeBase: 0.15,
                    fbaFulfillmentFee: true,
                    hasInboundPlacement: true,
                    hasLowInventoryFee: true,
                    hasStorageUtilization: true
                },
                'eBay': {
                    referralFeeBase: 0.129,      // 12.9% average
                    finalValueFee: true,
                    hasPromotedListings: true
                },
                'Shopee': {
                    referralFeeBase: 0.06,       // 6% commission
                    transactionFee: 0.02,        // 2% transaction fee
                    hasShippingSubsidy: true
                },
                'Lazada': {
                    referralFeeBase: 0.04,       // 4% commission
                    hasLogisticsService: true
                },
                'AliExpress': {
                    referralFeeBase: 0.08,       // 8% commission
                    hasGoldSupplier: true
                }
            };

            function calculateMetrics(item) {
                const platform = item.platform || 'Amazon';
                const category = item.category || 'default';

                // Basic calculations
                const revenue = item.quantity * item.price;
                const productCost = item.quantity * item.cost;

                // Platform-specific fee calculations
                let referralFee = 0;
                let platformSpecificFees = 0;

                switch(platform) {
                    case 'Amazon':
                        referralFee = calculateAmazonReferralFee(revenue, category, item.price);
                        platformSpecificFees = calculateAmazonSpecificFees(item);
                        break;
                    case 'eBay':
                        referralFee = revenue * 0.129; // eBay final value fee
                        platformSpecificFees = calculateEbaySpecificFees(item);
                        break;
                    case 'Shopee':
                        referralFee = revenue * 0.06;
                        platformSpecificFees = calculateShopeeSpecificFees(item);
                        break;
                    case 'Lazada':
                        referralFee = revenue * 0.04;
                        platformSpecificFees = calculateLazadaSpecificFees(item);
                        break;
                    case 'AliExpress':
                        referralFee = revenue * 0.08;
                        platformSpecificFees = calculateAliExpressSpecificFees(item);
                        break;
                    default:
                        referralFee = revenue * (item.referralFeeRate || 0.15);
                }

                // Common fees across platforms
                const fbaFee = item.quantity * (item.fbaFee || 0);
                const storageFee = item.quantity * (item.storageFee || 0);
                const longTermStorageFee = item.quantity * (item.longTermStorageFee || 0);
                const returnFee = revenue * (item.returnRate || 0);
                const closingFee = item.quantity * (item.closingFee || 0);
                const removalFee = item.quantity * (item.removalFee || 0);
                const inboundShipping = item.inboundShipping || 0;
                const adSpend = item.adSpend || 0;
                const taxes = item.taxes || 0;

                // Cross-border specific fees
                const exchangeRateLoss = revenue * (item.exchangeRateLoss || 0.02); // 2% average loss
                const customsDuty = revenue * (item.customsDutyRate || 0);
                const crossBorderLogistics = item.quantity * (item.crossBorderLogisticsFee || 0);
                const paymentProcessingFee = revenue * (item.paymentProcessingRate || 0.029); // 2.9% average

                // New Amazon 2024 fees
                const inboundPlacementFee = item.quantity * (item.inboundPlacementFee || 0.27); // $0.27 average for standard
                const lowInventoryFee = item.quantity * (item.lowInventoryFee || 0); // Applied when inventory < 28 days
                const storageUtilizationSurcharge = item.quantity * (item.storageUtilizationSurcharge || 0);
                const returnProcessingFee = calculateReturnProcessingFee(item, revenue);

                const totalCost = productCost + referralFee + fbaFee + storageFee + longTermStorageFee +
                                returnFee + closingFee + removalFee + inboundShipping + adSpend + taxes +
                                platformSpecificFees + exchangeRateLoss + customsDuty + crossBorderLogistics +
                                paymentProcessingFee + inboundPlacementFee + lowInventoryFee +
                                storageUtilizationSurcharge + returnProcessingFee;

                const netProfit = revenue - totalCost;
                const netProfitMargin = revenue > 0 ? (netProfit / revenue) : 0;

                return {
                    revenue,
                    productCost,
                    referralFee,
                    fbaFee,
                    storageFee,
                    longTermStorageFee,
                    returnFee,
                    closingFee,
                    removalFee,
                    inboundShipping,
                    adSpend,
                    taxes,
                    // New cross-border fees
                    exchangeRateLoss,
                    customsDuty,
                    crossBorderLogistics,
                    paymentProcessingFee,
                    // New Amazon 2024 fees
                    inboundPlacementFee,
                    lowInventoryFee,
                    storageUtilizationSurcharge,
                    returnProcessingFee,
                    platformSpecificFees,
                    totalCost,
                    netProfit,
                    netProfitMargin
                };
            }

            // Amazon-specific referral fee calculation with 2024 updates
            function calculateAmazonReferralFee(revenue, category, price) {
                let rate = amazonReferralRates[category] || amazonReferralRates.default;

                // Special rule for apparel under $20 (reduced to 5% from 17%)
                if (category === 'Clothing & Accessories' && price < 20) {
                    rate = 0.05;
                }

                return revenue * rate;
            }

            // Amazon-specific fees calculation
            function calculateAmazonSpecificFees(item) {
                let fees = 0;

                // FBA fulfillment fees (varies by size tier)
                if (item.sizeCategory === 'standard') {
                    fees += item.quantity * 3.50; // Average standard size fulfillment fee
                } else if (item.sizeCategory === 'large') {
                    fees += item.quantity * 8.50; // Average large size fulfillment fee
                }

                return fees;
            }

            // Return processing fee calculation (Amazon 2024 update)
            function calculateReturnProcessingFee(item, revenue) {
                const categoryThresholds = {
                    'Electronics': 0.08,
                    'Home & Garden': 0.15,
                    'Clothing & Accessories': 0.20, // Exempt from this fee
                    'default': 0.10
                };

                const threshold = categoryThresholds[item.category] || categoryThresholds.default;
                const returnRate = item.returnRate || 0;

                // Only charge if return rate exceeds category threshold
                if (returnRate > threshold && item.category !== 'Clothing & Accessories') {
                    return item.quantity * 0.65; // $0.65 per unit fee
                }

                return 0;
            }

            // Platform-specific fee calculations for other platforms
            function calculateEbaySpecificFees(item) {
                // eBay promoted listings fee
                return item.revenue * (item.promotedListingsRate || 0.02); // 2% average
            }

            function calculateShopeeSpecificFees(item) {
                // Shopee transaction fee
                return item.revenue * 0.02; // 2% transaction fee
            }

            function calculateLazadaSpecificFees(item) {
                // Lazada logistics service fee
                return item.quantity * (item.lazadaLogisticsFee || 2.0); // $2 average per unit
            }

            function calculateAliExpressSpecificFees(item) {
                // AliExpress Gold Supplier fee
                return item.goldSupplier ? 2999 / 12 : 0; // $2999 annual fee amortized monthly
            }

            let currentData = [...allData];
            let currentPage = 1;
            const rowsPerPage = 5;
            let sortColumn = null;
            let sortDirection = 'asc';

            // 2. 渲染逻辑 (Rendering Logic)
            function render(data) {
                renderDashboard(data);
                renderTodayDashboard(data);
                renderTrendChart(data);
                renderCategoryChart(data);
                renderPlatformChart(data);
                renderRegionChart(data);
                renderTodayCharts(data);
                renderAdvancedChart(data);
                renderProductsGrid(data);
                renderCustomersData(data);
                renderDataTable(data.slice((currentPage - 1) * rowsPerPage, currentPage * rowsPerPage));
                renderPagination(data);
            }

            // 格式化数字为千位分隔
            function formatNumber(num) {
                return num.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,');
            }

            // 渲染仪表盘
            function renderDashboard(data) {
                let totalRevenue = 0;
                let totalCost = 0;

                data.forEach(item => {
                    const { revenue, totalCost: tc } = calculateMetrics(item);
                    totalRevenue += revenue;
                    totalCost   += tc;
                });

                const netProfit = totalRevenue - totalCost;
                const netProfitMargin = totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0;

                document.querySelector('.dashboard .card:nth-child(1) .card-value').textContent = formatNumber(Math.round(totalRevenue));
                document.querySelector('.dashboard .card:nth-child(2) .card-value').textContent = formatNumber(Math.round(totalCost));
                document.querySelector('.dashboard .card:nth-child(3) .card-value').textContent = formatNumber(Math.round(netProfit));
                document.querySelector('.dashboard .card:nth-child(4) .card-value').textContent = netProfitMargin.toFixed(1) + '%';
            }

            // 渲染月度趋势图
            function renderTrendChart(data) {
                const chartDom = document.getElementById('trend-chart');
                if (!chartDom) return;

                chartDom.innerHTML = ''; // 清空占位符
                const myChart = echarts.init(chartDom);

                const monthlyData = {};
                data.forEach(item => {
                    const month = new Date(item.date).toISOString().slice(0, 7);
                    if (!monthlyData[month]) {
                        monthlyData[month] = { revenue: 0, cost: 0, profit: 0 };
                    }
                    const { revenue, totalCost } = calculateMetrics(item);

                    monthlyData[month].revenue += revenue;
                    monthlyData[month].cost    += totalCost;
                    monthlyData[month].profit  += (revenue - totalCost);
                });

                const sortedMonths = Object.keys(monthlyData).sort();

                const option = {
                    tooltip: { trigger: 'axis' },
                    legend: { data: ['收入', '成本', '净利润'], bottom: 0 },
                    grid: { top: '10%', left: '3%', right: '4%', bottom: '15%', containLabel: true },
                    xAxis: {
                        type: 'category',
                        boundaryGap: false,
                        data: sortedMonths,
                    },
                    yAxis: {
                        type: 'value',
                        axisLabel: {
                            formatter: (value) => `${(value / 10000).toFixed(0)}w`
                        }
                    },
                    series: [
                        { name: '收入', type: 'line', data: sortedMonths.map(m => monthlyData[m].revenue), smooth: true, itemStyle: { color: '#1890ff' } },
                        { name: '成本', type: 'line', data: sortedMonths.map(m => monthlyData[m].cost), smooth: true, itemStyle: { color: '#ffc53d' } },
                        { name: '净利润', type: 'line', data: sortedMonths.map(m => monthlyData[m].profit), smooth: true, itemStyle: { color: '#52c41a' } }
                    ]
                };
                myChart.setOption(option);

                // 存储图表实例以便后续使用
                window.trendChart = myChart;
            }

            // 渲染产品类别分布图
            function renderCategoryChart(data) {
                const chartDom = document.getElementById('category-pie-chart');
                if (!chartDom) return;

                chartDom.innerHTML = ''; // 清空占位符
                const myChart = echarts.init(chartDom);

                const categoryData = {};
                data.forEach(item => {
                    const { revenue, totalCost } = calculateMetrics(item);
                    const profit = revenue - totalCost;

                    if (!categoryData[item.category]) {
                        categoryData[item.category] = 0;
                    }
                    categoryData[item.category] += profit;
                });

                const chartData = Object.keys(categoryData).map(key => ({
                    name: key,
                    value: categoryData[key]
                }));

                const option = {
                    tooltip: { trigger: 'item', formatter: '{a} <br/>{b}: ¥{c} ({d}%)' },
                    legend: { orient: 'vertical', left: 'left' },
                    series: [
                        {
                            name: '净利润来源',
                            type: 'pie',
                            radius: '70%',
                            center: ['60%', '50%'],
                            data: chartData,
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            }
                        }
                    ]
                };
                myChart.setOption(option);

                // 存储图表实例
                window.categoryChart = myChart;
            }

            // 渲染平台对比图
            function renderPlatformChart(data) {
                const chartDom = document.getElementById('platform-chart');
                if (!chartDom) return;

                chartDom.innerHTML = '';
                const myChart = echarts.init(chartDom);

                const platformData = {};
                data.forEach(item => {
                    const platform = item.platform || 'Amazon';
                    const { revenue, totalCost } = calculateMetrics(item);
                    const profit = revenue - totalCost;

                    if (!platformData[platform]) {
                        platformData[platform] = { revenue: 0, profit: 0, count: 0 };
                    }
                    platformData[platform].revenue += revenue;
                    platformData[platform].profit += profit;
                    platformData[platform].count += 1;
                });

                const platforms = Object.keys(platformData);
                const revenueData = platforms.map(p => platformData[p].revenue);
                const profitData = platforms.map(p => platformData[p].profit);

                const option = {
                    tooltip: { trigger: 'axis' },
                    legend: { data: ['收入', '利润'], bottom: 0 },
                    grid: { top: '10%', left: '3%', right: '4%', bottom: '15%', containLabel: true },
                    xAxis: {
                        type: 'category',
                        data: platforms,
                        axisLabel: { rotate: 45 }
                    },
                    yAxis: {
                        type: 'value',
                        axisLabel: {
                            formatter: (value) => `${(value / 10000).toFixed(0)}w`
                        }
                    },
                    series: [
                        { name: '收入', type: 'bar', data: revenueData, itemStyle: { color: '#1890ff' } },
                        { name: '利润', type: 'bar', data: profitData, itemStyle: { color: '#52c41a' } }
                    ]
                };
                myChart.setOption(option);
                window.platformChart = myChart;
            }

            // 渲染区域分析图
            function renderRegionChart(data) {
                const chartDom = document.getElementById('region-chart');
                if (!chartDom) return;

                chartDom.innerHTML = '';
                const myChart = echarts.init(chartDom);

                const regionData = {};
                data.forEach(item => {
                    const region = item.region || '未知';
                    const { revenue, totalCost } = calculateMetrics(item);
                    const profit = revenue - totalCost;

                    if (!regionData[region]) {
                        regionData[region] = 0;
                    }
                    regionData[region] += profit;
                });

                const chartData = Object.keys(regionData).map(key => ({
                    name: key,
                    value: regionData[key]
                }));

                const option = {
                    tooltip: { trigger: 'item', formatter: '{a} <br/>{b}: ¥{c} ({d}%)' },
                    legend: { orient: 'vertical', left: 'left' },
                    series: [
                        {
                            name: '区域利润分布',
                            type: 'pie',
                            radius: ['40%', '70%'],
                            center: ['60%', '50%'],
                            data: chartData,
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            }
                        }
                    ]
                };
                myChart.setOption(option);
                window.regionChart = myChart;
            }

            // 渲染今日数据看板
            function renderTodayDashboard(data) {
                // 获取最新的数据日期作为"今日"数据，如果没有当日数据则使用最新日期的数据
                const today = new Date().toISOString().slice(0, 10);
                let todayData = data.filter(item => item.date === today);

                // 如果没有今日数据，使用最新日期的数据作为"今日"数据
                if (todayData.length === 0 && data.length > 0) {
                    const latestDate = data.reduce((latest, item) => {
                        return item.date > latest ? item.date : latest;
                    }, data[0].date);
                    todayData = data.filter(item => item.date === latestDate);

                    // 更新显示的日期为最新数据日期
                    const todayDateElement = document.getElementById('today-date');
                    if (todayDateElement) {
                        const displayDate = new Date(latestDate);
                        todayDateElement.textContent = displayDate.toLocaleDateString('zh-CN', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                            weekday: 'long'
                        }) + ' (最新数据)';
                    }
                } else {
                    // 更新今日日期显示
                    const todayDateElement = document.getElementById('today-date');
                    if (todayDateElement) {
                        todayDateElement.textContent = new Date().toLocaleDateString('zh-CN', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                            weekday: 'long'
                        });
                    }
                }

                let todayRevenue = 0;
                let todayProfit = 0;
                let todayOrders = todayData.length;

                todayData.forEach(item => {
                    const { revenue, totalCost } = calculateMetrics(item);
                    todayRevenue += revenue;
                    todayProfit += (revenue - totalCost);
                });

                const todayMargin = todayRevenue > 0 ? (todayProfit / todayRevenue) * 100 : 0;

                // 计算昨日数据用于对比
                const yesterday = new Date();
                yesterday.setDate(yesterday.getDate() - 1);
                const yesterdayStr = yesterday.toISOString().slice(0, 10);
                const yesterdayData = data.filter(item => item.date === yesterdayStr);

                let yesterdayRevenue = 0;
                let yesterdayProfit = 0;
                yesterdayData.forEach(item => {
                    const { revenue, totalCost } = calculateMetrics(item);
                    yesterdayRevenue += revenue;
                    yesterdayProfit += (revenue - totalCost);
                });

                // 计算增长率
                const revenueGrowth = yesterdayRevenue > 0 ? ((todayRevenue - yesterdayRevenue) / yesterdayRevenue * 100) : 0;
                const profitGrowth = yesterdayProfit > 0 ? ((todayProfit - yesterdayProfit) / yesterdayProfit * 100) : 0;
                const ordersGrowth = todayOrders - yesterdayData.length;

                // 更新今日数据显示
                updateTodayElement('today-revenue', formatNumber(Math.round(todayRevenue)));
                updateTodayElement('today-orders', todayOrders);
                updateTodayElement('today-profit', formatNumber(Math.round(todayProfit)));
                updateTodayElement('today-margin', todayMargin.toFixed(1) + '%');

                // 更新趋势显示
                updateTodayTrend('today-revenue-trend', revenueGrowth, '%');
                updateTodayTrend('today-orders-trend', ordersGrowth, '');
                updateTodayTrend('today-profit-trend', profitGrowth, '%');

                // 生成今日提醒
                generateTodayAlerts(todayData, revenueGrowth, profitGrowth);
            }

            function updateTodayElement(id, value) {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                }
            }

            function updateTodayTrend(id, value, suffix) {
                const element = document.getElementById(id);
                if (element) {
                    const sign = value >= 0 ? '+' : '';
                    element.textContent = `${sign}${value.toFixed(1)}${suffix}`;
                    element.className = value >= 0 ? 'card-trend trend-up' : 'card-trend trend-down';
                }
            }

            function generateTodayAlerts(todayData, revenueGrowth, profitGrowth) {
                const alertsContainer = document.getElementById('today-alerts');
                if (!alertsContainer) return;

                const alerts = [];

                // 收入增长提醒
                if (revenueGrowth > 10) {
                    alerts.push({
                        type: 'success',
                        icon: '📈',
                        message: `今日收入增长${revenueGrowth.toFixed(1)}%，表现优异！`
                    });
                } else if (revenueGrowth < -10) {
                    alerts.push({
                        type: 'warning',
                        icon: '📉',
                        message: `今日收入下降${Math.abs(revenueGrowth).toFixed(1)}%，需要关注`
                    });
                }

                // 利润率提醒
                const avgMargin = todayData.length > 0 ?
                    todayData.reduce((sum, item) => {
                        const { revenue, totalCost } = calculateMetrics(item);
                        return sum + (revenue > 0 ? ((revenue - totalCost) / revenue) : 0);
                    }, 0) / todayData.length * 100 : 0;

                if (avgMargin < 10) {
                    alerts.push({
                        type: 'warning',
                        icon: '⚠️',
                        message: `今日平均利润率仅${avgMargin.toFixed(1)}%，建议优化成本结构`
                    });
                }

                // 订单数量提醒
                if (todayData.length === 0) {
                    alerts.push({
                        type: 'info',
                        icon: 'ℹ️',
                        message: '今日暂无订单数据'
                    });
                } else if (todayData.length > 10) {
                    alerts.push({
                        type: 'success',
                        icon: '🎉',
                        message: `今日订单量${todayData.length}单，业务活跃！`
                    });
                }

                // 渲染提醒
                alertsContainer.innerHTML = alerts.length > 0 ?
                    alerts.map(alert => `
                        <div class="alert-item ${alert.type}">
                            <span class="alert-icon">${alert.icon}</span>
                            ${alert.message}
                        </div>
                    `).join('') :
                    '<div class="alert-item info"><span class="alert-icon">ℹ️</span>暂无特别提醒</div>';
            }

            // 渲染今日图表
            function renderTodayCharts(data) {
                const today = new Date().toISOString().slice(0, 10);
                const todayData = data.filter(item => item.date === today);

                renderTodayPlatformChart(todayData);
                renderTodayProductsChart(todayData);
            }

            function renderTodayPlatformChart(todayData) {
                const chartDom = document.getElementById('today-platform-chart');
                if (!chartDom) return;

                chartDom.innerHTML = '';
                const myChart = echarts.init(chartDom);

                const platformData = {};
                todayData.forEach(item => {
                    const platform = item.platform || 'Amazon';
                    const { revenue } = calculateMetrics(item);

                    if (!platformData[platform]) {
                        platformData[platform] = 0;
                    }
                    platformData[platform] += revenue;
                });

                const chartData = Object.keys(platformData).map(key => ({
                    name: key,
                    value: platformData[key]
                }));

                const option = {
                    tooltip: { trigger: 'item', formatter: '{a} <br/>{b}: ¥{c} ({d}%)' },
                    legend: { orient: 'vertical', left: 'left' },
                    series: [
                        {
                            name: '今日平台销售',
                            type: 'pie',
                            radius: '70%',
                            center: ['60%', '50%'],
                            data: chartData,
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            }
                        }
                    ]
                };
                myChart.setOption(option);
                window.todayPlatformChart = myChart;
            }

            function renderTodayProductsChart(todayData) {
                const chartDom = document.getElementById('today-products-chart');
                if (!chartDom) return;

                chartDom.innerHTML = '';
                const myChart = echarts.init(chartDom);

                // 按产品统计销售额
                const productData = {};
                todayData.forEach(item => {
                    const { revenue } = calculateMetrics(item);
                    if (!productData[item.name]) {
                        productData[item.name] = 0;
                    }
                    productData[item.name] += revenue;
                });

                // 取前5名
                const sortedProducts = Object.entries(productData)
                    .sort(([,a], [,b]) => b - a)
                    .slice(0, 5);

                const productNames = sortedProducts.map(([name]) => name);
                const productValues = sortedProducts.map(([,value]) => value);

                const option = {
                    tooltip: { trigger: 'axis' },
                    grid: { top: '10%', left: '3%', right: '4%', bottom: '15%', containLabel: true },
                    xAxis: {
                        type: 'category',
                        data: productNames,
                        axisLabel: {
                            rotate: 45,
                            formatter: function(value) {
                                return value.length > 10 ? value.substring(0, 10) + '...' : value;
                            }
                        }
                    },
                    yAxis: {
                        type: 'value',
                        axisLabel: {
                            formatter: (value) => `${(value / 1000).toFixed(0)}k`
                        }
                    },
                    series: [
                        {
                            name: '销售额',
                            type: 'bar',
                            data: productValues,
                            itemStyle: { color: '#1890ff' }
                        }
                    ]
                };
                myChart.setOption(option);
                window.todayProductsChart = myChart;
            }

            // 高级图表管理系统
            class AdvancedChartManager {
                constructor() {
                    this.currentChart = null;
                    this.currentChartType = 'heatmap';
                    this.drillDownStack = [];
                    this.chartData = null;
                }

                renderChart(data, chartType, dimension, timeRange) {
                    const chartContainer = document.getElementById('advanced-chart-content');
                    if (!chartContainer) return;

                    this.currentChartType = chartType;
                    this.chartData = this.processData(data, dimension, timeRange);

                    chartContainer.innerHTML = '';

                    if (this.currentChart) {
                        this.currentChart.dispose();
                    }

                    this.currentChart = echarts.init(chartContainer);

                    let option;
                    switch (chartType) {
                        case 'heatmap':
                            option = this.createHeatmapOption(this.chartData, dimension);
                            break;
                        case 'scatter':
                            option = this.createScatterOption(this.chartData, dimension);
                            break;
                        case 'radar':
                            option = this.createRadarOption(this.chartData, dimension);
                            break;
                        case 'sankey':
                            option = this.createSankeyOption(this.chartData, dimension);
                            break;
                        case 'treemap':
                            option = this.createTreemapOption(this.chartData, dimension);
                            break;
                        case 'funnel':
                            option = this.createFunnelOption(this.chartData, dimension);
                            break;
                        default:
                            option = this.createHeatmapOption(this.chartData, dimension);
                    }

                    this.currentChart.setOption(option);
                    this.updateChartInfo(this.chartData);
                    this.setupChartInteractions();

                    // 更新图表标题
                    const titleElement = document.getElementById('advanced-chart-title');
                    if (titleElement) {
                        const chartNames = {
                            'heatmap': '热力图',
                            'scatter': '散点图',
                            'radar': '雷达图',
                            'sankey': '桑基图',
                            'treemap': '树状图',
                            'funnel': '漏斗图'
                        };
                        titleElement.textContent = `${chartNames[chartType]} - ${this.getDimensionName(dimension)}`;
                    }
                }

                processData(data, dimension, timeRange) {
                    // 根据时间范围过滤数据
                    let filteredData = data;
                    if (timeRange !== 'all') {
                        const days = parseInt(timeRange);
                        const cutoffDate = new Date();
                        cutoffDate.setDate(cutoffDate.getDate() - days);
                        filteredData = data.filter(item => new Date(item.date) >= cutoffDate);
                    }

                    // 根据维度处理数据
                    const processedData = {};
                    filteredData.forEach(item => {
                        const metrics = calculateMetrics(item);
                        let key;

                        switch (dimension) {
                            case 'category':
                                key = item.category;
                                break;
                            case 'platform':
                                key = item.platform;
                                break;
                            case 'region':
                                key = item.region;
                                break;
                            case 'month':
                                key = new Date(item.date).toISOString().slice(0, 7);
                                break;
                            default:
                                key = item.category;
                        }

                        if (!processedData[key]) {
                            processedData[key] = {
                                name: key,
                                revenue: 0,
                                profit: 0,
                                cost: 0,
                                quantity: 0,
                                count: 0,
                                items: []
                            };
                        }

                        processedData[key].revenue += metrics.revenue;
                        processedData[key].profit += metrics.netProfit;
                        processedData[key].cost += metrics.totalCost;
                        processedData[key].quantity += item.quantity;
                        processedData[key].count += 1;
                        processedData[key].items.push(item);
                    });

                    return Object.values(processedData);
                }

                createHeatmapOption(data, dimension) {
                    const xData = data.map(d => d.name);
                    const yData = ['收入', '利润', '成本', '数量'];
                    const heatmapData = [];

                    data.forEach((item, i) => {
                        heatmapData.push([i, 0, item.revenue]);
                        heatmapData.push([i, 1, item.profit]);
                        heatmapData.push([i, 2, item.cost]);
                        heatmapData.push([i, 3, item.quantity]);
                    });

                    return {
                        tooltip: {
                            position: 'top',
                            formatter: function(params) {
                                return `${xData[params.data[0]]}<br/>${yData[params.data[1]]}: ${formatNumber(params.data[2])}`;
                            }
                        },
                        grid: {
                            height: '50%',
                            top: '10%'
                        },
                        xAxis: {
                            type: 'category',
                            data: xData,
                            splitArea: { show: true }
                        },
                        yAxis: {
                            type: 'category',
                            data: yData,
                            splitArea: { show: true }
                        },
                        visualMap: {
                            min: 0,
                            max: Math.max(...heatmapData.map(d => d[2])),
                            calculable: true,
                            orient: 'horizontal',
                            left: 'center',
                            bottom: '15%'
                        },
                        series: [{
                            name: '数据热力图',
                            type: 'heatmap',
                            data: heatmapData,
                            label: {
                                show: true,
                                formatter: function(params) {
                                    return formatNumber(params.data[2]);
                                }
                            },
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            }
                        }]
                    };
                }

                createScatterOption(data, dimension) {
                    const scatterData = data.map(item => [
                        item.revenue,
                        item.profit,
                        item.quantity,
                        item.name
                    ]);

                    return {
                        tooltip: {
                            trigger: 'item',
                            formatter: function(params) {
                                return `${params.data[3]}<br/>收入: ¥${formatNumber(params.data[0])}<br/>利润: ¥${formatNumber(params.data[1])}<br/>数量: ${params.data[2]}`;
                            }
                        },
                        xAxis: {
                            type: 'value',
                            name: '收入 (元)',
                            axisLabel: {
                                formatter: (value) => formatNumber(value)
                            }
                        },
                        yAxis: {
                            type: 'value',
                            name: '利润 (元)',
                            axisLabel: {
                                formatter: (value) => formatNumber(value)
                            }
                        },
                        series: [{
                            name: '收入vs利润',
                            type: 'scatter',
                            data: scatterData,
                            symbolSize: function(data) {
                                return Math.sqrt(data[2]) * 2;
                            },
                            emphasis: {
                                focus: 'series',
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            }
                        }]
                    };
                }

                createRadarOption(data, dimension) {
                    const maxValues = {
                        revenue: Math.max(...data.map(d => d.revenue)),
                        profit: Math.max(...data.map(d => d.profit)),
                        cost: Math.max(...data.map(d => d.cost)),
                        quantity: Math.max(...data.map(d => d.quantity)),
                        count: Math.max(...data.map(d => d.count))
                    };

                    const indicator = [
                        { name: '收入', max: maxValues.revenue },
                        { name: '利润', max: maxValues.profit },
                        { name: '成本', max: maxValues.cost },
                        { name: '数量', max: maxValues.quantity },
                        { name: '订单数', max: maxValues.count }
                    ];

                    const radarData = data.slice(0, 5).map(item => ({
                        value: [item.revenue, item.profit, item.cost, item.quantity, item.count],
                        name: item.name
                    }));

                    return {
                        tooltip: {
                            trigger: 'item'
                        },
                        legend: {
                            data: radarData.map(d => d.name),
                            bottom: 0
                        },
                        radar: {
                            indicator: indicator,
                            center: ['50%', '50%'],
                            radius: '60%'
                        },
                        series: [{
                            name: '多维度分析',
                            type: 'radar',
                            data: radarData
                        }]
                    };
                }

                createTreemapOption(data, dimension) {
                    const treemapData = data.map(item => ({
                        name: item.name,
                        value: item.revenue,
                        itemStyle: {
                            color: item.profit > 0 ? '#52c41a' : '#ff4d4f'
                        }
                    }));

                    return {
                        tooltip: {
                            trigger: 'item',
                            formatter: function(params) {
                                const item = data.find(d => d.name === params.name);
                                return `${params.name}<br/>收入: ¥${formatNumber(params.value)}<br/>利润: ¥${formatNumber(item.profit)}`;
                            }
                        },
                        series: [{
                            name: '收入分布',
                            type: 'treemap',
                            data: treemapData,
                            roam: false,
                            nodeClick: 'zoomToNode',
                            breadcrumb: {
                                show: false
                            },
                            label: {
                                show: true,
                                formatter: '{b}\n¥{c}'
                            },
                            upperLabel: {
                                show: true,
                                height: 30
                            }
                        }]
                    };
                }

                createFunnelOption(data, dimension) {
                    const sortedData = data.sort((a, b) => b.revenue - a.revenue);
                    const funnelData = sortedData.slice(0, 6).map(item => ({
                        name: item.name,
                        value: item.revenue
                    }));

                    return {
                        tooltip: {
                            trigger: 'item',
                            formatter: '{a} <br/>{b}: ¥{c} ({d}%)'
                        },
                        legend: {
                            data: funnelData.map(d => d.name),
                            bottom: 0
                        },
                        series: [{
                            name: '收入漏斗',
                            type: 'funnel',
                            left: '10%',
                            top: 60,
                            bottom: 60,
                            width: '80%',
                            min: 0,
                            max: Math.max(...funnelData.map(d => d.value)),
                            minSize: '0%',
                            maxSize: '100%',
                            sort: 'descending',
                            gap: 2,
                            label: {
                                show: true,
                                position: 'inside'
                            },
                            labelLine: {
                                length: 10,
                                lineStyle: {
                                    width: 1,
                                    type: 'solid'
                                }
                            },
                            itemStyle: {
                                borderColor: '#fff',
                                borderWidth: 1
                            },
                            emphasis: {
                                label: {
                                    fontSize: 20
                                }
                            },
                            data: funnelData
                        }]
                    };
                }

                createSankeyOption(data, dimension) {
                    // 简化的桑基图数据
                    const nodes = [];
                    const links = [];

                    // 创建源节点（类别）
                    const categories = [...new Set(data.map(d => d.name))];
                    categories.forEach(cat => {
                        nodes.push({ name: cat });
                    });

                    // 创建目标节点（利润等级）
                    const profitLevels = ['高利润', '中利润', '低利润', '亏损'];
                    profitLevels.forEach(level => {
                        nodes.push({ name: level });
                    });

                    // 创建连接
                    data.forEach(item => {
                        let profitLevel;
                        if (item.profit > 10000) profitLevel = '高利润';
                        else if (item.profit > 5000) profitLevel = '中利润';
                        else if (item.profit > 0) profitLevel = '低利润';
                        else profitLevel = '亏损';

                        links.push({
                            source: item.name,
                            target: profitLevel,
                            value: Math.abs(item.profit)
                        });
                    });

                    return {
                        tooltip: {
                            trigger: 'item',
                            triggerOn: 'mousemove'
                        },
                        series: [{
                            type: 'sankey',
                            data: nodes,
                            links: links,
                            emphasis: {
                                focus: 'adjacency'
                            },
                            lineStyle: {
                                color: 'gradient',
                                curveness: 0.5
                            }
                        }]
                    };
                }

                updateChartInfo(data) {
                    const values = data.map(d => d.revenue);
                    const maxValue = Math.max(...values);
                    const minValue = Math.min(...values);
                    const avgValue = values.reduce((sum, val) => sum + val, 0) / values.length;

                    document.getElementById('data-points-count').textContent = data.length;
                    document.getElementById('max-value').textContent = formatNumber(maxValue);
                    document.getElementById('min-value').textContent = formatNumber(minValue);
                    document.getElementById('avg-value').textContent = formatNumber(avgValue);
                }

                setupChartInteractions() {
                    if (!this.currentChart) return;

                    this.currentChart.on('click', (params) => {
                        // 数据钻取功能
                        if (params.data && params.data.name) {
                            this.drillDown(params.data.name);
                        }
                    });
                }

                drillDown(itemName) {
                    // 实现数据钻取逻辑
                    this.drillDownStack.push({
                        chartType: this.currentChartType,
                        data: this.chartData,
                        level: itemName
                    });

                    // 更新钻取路径显示
                    this.updateDrillDownPath();
                    notifications.info(`钻取到: ${itemName}`);
                }

                drillUp() {
                    if (this.drillDownStack.length > 0) {
                        const previous = this.drillDownStack.pop();
                        this.updateDrillDownPath();
                        notifications.info('返回上一级');
                    }
                }

                resetDrill() {
                    this.drillDownStack = [];
                    this.updateDrillDownPath();
                    notifications.info('重置钻取');
                }

                updateDrillDownPath() {
                    const pathElement = document.getElementById('drill-down-path');
                    if (pathElement) {
                        const path = this.drillDownStack.map(item => item.level).join(' > ');
                        pathElement.textContent = path || '根级别';
                    }
                }

                getDimensionName(dimension) {
                    const names = {
                        'category': '产品类别',
                        'platform': '销售平台',
                        'region': '销售区域',
                        'month': '月份'
                    };
                    return names[dimension] || '未知维度';
                }

                exportChart() {
                    if (this.currentChart) {
                        const url = this.currentChart.getDataURL({
                            pixelRatio: 2,
                            backgroundColor: '#fff'
                        });

                        const link = document.createElement('a');
                        link.download = `chart_${Date.now()}.png`;
                        link.href = url;
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);

                        notifications.success('图表导出成功');
                    }
                }

                toggleFullscreen() {
                    const chartMain = document.querySelector('.chart-main');
                    if (chartMain) {
                        if (chartMain.classList.contains('chart-fullscreen')) {
                            chartMain.classList.remove('chart-fullscreen');
                            document.body.style.overflow = '';
                        } else {
                            chartMain.classList.add('chart-fullscreen');
                            document.body.style.overflow = 'hidden';
                        }

                        setTimeout(() => {
                            if (this.currentChart) {
                                this.currentChart.resize();
                            }
                        }, 100);
                    }
                }
            }

            // 初始化高级图表管理器
            const advancedChartManager = new AdvancedChartManager();
            window.advancedChartManager = advancedChartManager;

            // 批量操作管理系统
            class BatchOperationManager {
                constructor() {
                    this.selectedItems = new Set();
                    this.allItems = [];
                }

                // 设置所有项目
                setAllItems(items) {
                    this.allItems = items;
                    this.updateUI();
                }

                // 选择项目
                selectItem(itemId) {
                    this.selectedItems.add(itemId);
                    this.updateUI();
                }

                // 取消选择项目
                deselectItem(itemId) {
                    this.selectedItems.delete(itemId);
                    this.updateUI();
                }

                // 切换项目选择状态
                toggleItem(itemId) {
                    if (this.selectedItems.has(itemId)) {
                        this.deselectItem(itemId);
                    } else {
                        this.selectItem(itemId);
                    }
                }

                // 全选
                selectAll() {
                    this.allItems.forEach(item => {
                        this.selectedItems.add(item.id);
                    });
                    this.updateUI();
                }

                // 取消全选
                deselectAll() {
                    this.selectedItems.clear();
                    this.updateUI();
                }

                // 切换全选状态
                toggleSelectAll() {
                    if (this.selectedItems.size === this.allItems.length) {
                        this.deselectAll();
                    } else {
                        this.selectAll();
                    }
                }

                // 获取选中的项目
                getSelectedItems() {
                    return this.allItems.filter(item => this.selectedItems.has(item.id));
                }

                // 获取选中的项目ID
                getSelectedIds() {
                    return Array.from(this.selectedItems);
                }

                // 批量删除
                batchDelete() {
                    if (this.selectedItems.size === 0) {
                        notifications.warning('请先选择要删除的项目');
                        return;
                    }

                    const count = this.selectedItems.size;
                    if (confirm(`确定要删除选中的 ${count} 个项目吗？此操作不可撤销。`)) {
                        try {
                            loading.show('正在删除...');

                            setTimeout(() => {
                                // 从allData中删除选中的项目
                                const selectedIds = this.getSelectedIds();
                                for (let i = allData.length - 1; i >= 0; i--) {
                                    if (selectedIds.includes(allData[i].id)) {
                                        allData.splice(i, 1);
                                    }
                                }

                                currentData = [...allData];
                                this.selectedItems.clear();
                                render(allData);
                                loading.hide();
                                notifications.success(`成功删除 ${count} 个项目`);
                            }, 1000);

                        } catch (error) {
                            loading.hide();
                            console.error('批量删除失败:', error);
                            notifications.error('删除失败：' + error.message);
                        }
                    }
                }

                // 批量导出
                batchExport() {
                    if (this.selectedItems.size === 0) {
                        notifications.warning('请先选择要导出的项目');
                        return;
                    }

                    try {
                        const selectedData = this.getSelectedItems();
                        exportToCSV(selectedData, `selected_data_${Date.now()}.csv`);
                        notifications.success(`成功导出 ${selectedData.length} 个项目`);
                    } catch (error) {
                        console.error('批量导出失败:', error);
                        notifications.error('导出失败：' + error.message);
                    }
                }

                // 批量编辑
                batchEdit(editData) {
                    if (this.selectedItems.size === 0) {
                        notifications.warning('请先选择要编辑的项目');
                        return;
                    }

                    try {
                        loading.show('正在批量编辑...');

                        setTimeout(() => {
                            const selectedIds = this.getSelectedIds();
                            let updatedCount = 0;

                            allData.forEach(item => {
                                if (selectedIds.includes(item.id)) {
                                    // 应用编辑
                                    if (editData.category) item.category = editData.category;
                                    if (editData.platform) item.platform = editData.platform;
                                    if (editData.region) item.region = editData.region;

                                    // 价格调整
                                    if (editData.priceAdjustment) {
                                        const { type, value } = editData.priceAdjustment;
                                        if (type === 'percentage') {
                                            item.price = item.price * (1 + value / 100);
                                        } else if (type === 'fixed') {
                                            item.price = item.price + value;
                                        }
                                        // 确保价格不为负数
                                        item.price = Math.max(0, item.price);
                                    }

                                    updatedCount++;
                                }
                            });

                            currentData = [...allData];
                            this.selectedItems.clear();
                            render(allData);
                            loading.hide();
                            notifications.success(`成功编辑 ${updatedCount} 个项目`);
                        }, 1000);

                    } catch (error) {
                        loading.hide();
                        console.error('批量编辑失败:', error);
                        notifications.error('编辑失败：' + error.message);
                    }
                }

                // 数据验证
                validateData() {
                    const validationResults = {
                        errors: [],
                        warnings: [],
                        success: []
                    };

                    this.allItems.forEach((item, index) => {
                        // 检查必填字段
                        if (!item.name || item.name.trim() === '') {
                            validationResults.errors.push(`第${index + 1}行：产品名称为空`);
                        }

                        if (!item.price || item.price <= 0) {
                            validationResults.errors.push(`第${index + 1}行：价格无效`);
                        }

                        if (item.cost < 0) {
                            validationResults.errors.push(`第${index + 1}行：成本不能为负数`);
                        }

                        // 检查业务逻辑
                        if (item.cost > item.price) {
                            validationResults.warnings.push(`第${index + 1}行：成本高于价格，可能亏损`);
                        }

                        if (item.quantity <= 0) {
                            validationResults.warnings.push(`第${index + 1}行：销售数量为0或负数`);
                        }

                        // 检查数据合理性
                        const metrics = calculateMetrics(item);
                        if (metrics.netProfitMargin > 0.8) {
                            validationResults.warnings.push(`第${index + 1}行：利润率过高(${(metrics.netProfitMargin * 100).toFixed(1)}%)，请检查数据`);
                        }
                    });

                    // 显示验证结果
                    this.showValidationResults(validationResults);
                }

                // 重复检查
                checkDuplicates() {
                    const duplicates = [];
                    const nameMap = new Map();

                    this.allItems.forEach((item, index) => {
                        const key = item.name.toLowerCase().trim();
                        if (nameMap.has(key)) {
                            duplicates.push({
                                name: item.name,
                                indices: [nameMap.get(key), index + 1]
                            });
                        } else {
                            nameMap.set(key, index + 1);
                        }
                    });

                    if (duplicates.length > 0) {
                        const message = duplicates.map(dup =>
                            `"${dup.name}" 在第 ${dup.indices.join(', ')} 行重复`
                        ).join('\n');
                        notifications.warning(`发现 ${duplicates.length} 组重复数据：\n${message}`);
                    } else {
                        notifications.success('未发现重复数据');
                    }
                }

                // 显示验证结果
                showValidationResults(results) {
                    const totalErrors = results.errors.length;
                    const totalWarnings = results.warnings.length;

                    if (totalErrors === 0 && totalWarnings === 0) {
                        notifications.success('数据验证通过，未发现问题');
                        return;
                    }

                    let message = `数据验证完成：\n`;
                    if (totalErrors > 0) {
                        message += `错误：${totalErrors} 个\n`;
                        message += results.errors.slice(0, 5).join('\n');
                        if (totalErrors > 5) {
                            message += `\n... 还有 ${totalErrors - 5} 个错误`;
                        }
                    }

                    if (totalWarnings > 0) {
                        message += `\n警告：${totalWarnings} 个\n`;
                        message += results.warnings.slice(0, 3).join('\n');
                        if (totalWarnings > 3) {
                            message += `\n... 还有 ${totalWarnings - 3} 个警告`;
                        }
                    }

                    if (totalErrors > 0) {
                        notifications.error(message);
                    } else {
                        notifications.warning(message);
                    }
                }

                // 更新UI
                updateUI() {
                    const selectedCount = this.selectedItems.size;
                    const totalCount = this.allItems.length;

                    // 更新选择计数
                    const countElement = document.getElementById('selected-count');
                    if (countElement) {
                        countElement.textContent = `已选择 ${selectedCount} 项`;
                    }

                    // 更新全选复选框状态
                    const selectAllCheckbox = document.getElementById('select-all-checkbox');
                    const headerSelectAll = document.getElementById('header-select-all');

                    if (selectAllCheckbox) {
                        selectAllCheckbox.checked = selectedCount === totalCount && totalCount > 0;
                        selectAllCheckbox.indeterminate = selectedCount > 0 && selectedCount < totalCount;
                    }

                    if (headerSelectAll) {
                        headerSelectAll.checked = selectedCount === totalCount && totalCount > 0;
                        headerSelectAll.indeterminate = selectedCount > 0 && selectedCount < totalCount;
                    }

                    // 显示/隐藏批量操作按钮
                    const batchActions = document.getElementById('batch-actions');
                    if (batchActions) {
                        batchActions.style.display = selectedCount > 0 ? 'flex' : 'none';
                    }

                    // 更新表格行的选中状态
                    this.updateTableRowSelection();
                }

                // 更新表格行选中状态
                updateTableRowSelection() {
                    const tableRows = document.querySelectorAll('#data-table-body tr');
                    tableRows.forEach(row => {
                        const checkbox = row.querySelector('input[type="checkbox"]');
                        if (checkbox) {
                            const itemId = parseInt(checkbox.value);
                            const isSelected = this.selectedItems.has(itemId);
                            checkbox.checked = isSelected;
                            row.classList.toggle('selected', isSelected);
                        }
                    });
                }
            }

            // 初始化批量操作管理器
            const batchManager = new BatchOperationManager();
            window.batchManager = batchManager;

            // 数据备份和恢复管理系统
            class BackupManager {
                constructor() {
                    this.backups = this.loadBackups();
                    this.autoBackupSettings = this.loadAutoBackupSettings();
                    this.setupAutoBackup();
                }

                // 创建备份
                createBackup(name, description) {
                    try {
                        const backupId = Date.now().toString();
                        const backup = {
                            id: backupId,
                            name: name || `备份_${new Date().toLocaleDateString()}`,
                            description: description || '',
                            data: JSON.parse(JSON.stringify(allData)), // 深拷贝
                            createdAt: new Date().toISOString(),
                            size: this.calculateDataSize(allData),
                            version: '1.0',
                            checksum: this.calculateChecksum(allData)
                        };

                        this.backups[backupId] = backup;
                        this.saveBackups();

                        return backupId;
                    } catch (error) {
                        console.error('创建备份失败:', error);
                        throw new Error('备份创建失败：' + error.message);
                    }
                }

                // 删除备份
                deleteBackup(backupId) {
                    if (this.backups[backupId]) {
                        delete this.backups[backupId];
                        this.saveBackups();
                        return true;
                    }
                    return false;
                }

                // 恢复数据
                restoreFromBackup(backupId) {
                    const backup = this.backups[backupId];
                    if (!backup) {
                        throw new Error('备份不存在');
                    }

                    try {
                        // 验证备份完整性
                        const currentChecksum = this.calculateChecksum(backup.data);
                        if (currentChecksum !== backup.checksum) {
                            throw new Error('备份文件已损坏，校验失败');
                        }

                        // 恢复数据
                        allData.length = 0;
                        allData.push(...backup.data);
                        currentData = [...allData];

                        // 重新渲染
                        render(allData);

                        return true;
                    } catch (error) {
                        console.error('恢复备份失败:', error);
                        throw new Error('恢复失败：' + error.message);
                    }
                }

                // 从文件恢复
                restoreFromFile(file) {
                    return new Promise((resolve, reject) => {
                        const reader = new FileReader();

                        reader.onload = (e) => {
                            try {
                                const content = e.target.result;
                                let data;

                                if (file.name.endsWith('.json')) {
                                    const backup = JSON.parse(content);
                                    data = backup.data || backup; // 支持直接数据或备份格式
                                } else if (file.name.endsWith('.csv')) {
                                    // CSV恢复逻辑
                                    data = this.parseCSVForRestore(content);
                                } else {
                                    throw new Error('不支持的文件格式');
                                }

                                // 验证数据格式
                                if (!Array.isArray(data)) {
                                    throw new Error('数据格式错误');
                                }

                                // 恢复数据
                                allData.length = 0;
                                allData.push(...data);
                                currentData = [...allData];

                                render(allData);
                                resolve(data.length);

                            } catch (error) {
                                reject(error);
                            }
                        };

                        reader.onerror = () => {
                            reject(new Error('文件读取失败'));
                        };

                        reader.readAsText(file, 'UTF-8');
                    });
                }

                // 导出备份到文件
                exportBackup(backupId) {
                    const backup = this.backups[backupId];
                    if (!backup) {
                        throw new Error('备份不存在');
                    }

                    const dataStr = JSON.stringify(backup, null, 2);
                    const blob = new Blob([dataStr], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);

                    const link = document.createElement('a');
                    link.download = `backup_${backup.name}_${backup.id}.json`;
                    link.href = url;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    URL.revokeObjectURL(url);
                }

                // 获取所有备份
                getAllBackups() {
                    return Object.values(this.backups).sort((a, b) =>
                        new Date(b.createdAt) - new Date(a.createdAt)
                    );
                }

                // 设置自动备份
                setupAutoBackup() {
                    if (this.autoBackupSettings.enabled) {
                        this.scheduleAutoBackup();
                    }
                }

                // 调度自动备份
                scheduleAutoBackup() {
                    const { frequency } = this.autoBackupSettings;
                    let interval;

                    switch (frequency) {
                        case 'daily':
                            interval = 24 * 60 * 60 * 1000; // 24小时
                            break;
                        case 'weekly':
                            interval = 7 * 24 * 60 * 60 * 1000; // 7天
                            break;
                        case 'monthly':
                            interval = 30 * 24 * 60 * 60 * 1000; // 30天
                            break;
                        default:
                            return;
                    }

                    // 检查是否需要自动备份
                    const lastAutoBackup = this.getLastAutoBackup();
                    const now = Date.now();

                    if (!lastAutoBackup || (now - new Date(lastAutoBackup.createdAt).getTime()) >= interval) {
                        this.performAutoBackup();
                    }

                    // 设置定时器
                    setTimeout(() => {
                        this.performAutoBackup();
                        this.scheduleAutoBackup(); // 递归调度
                    }, interval);
                }

                // 执行自动备份
                performAutoBackup() {
                    try {
                        const name = `自动备份_${new Date().toLocaleDateString()}`;
                        const description = `系统自动创建的备份 - ${this.autoBackupSettings.frequency}`;

                        this.createBackup(name, description);
                        this.cleanupOldAutoBackups();

                        notifications.info('自动备份已创建');
                    } catch (error) {
                        console.error('自动备份失败:', error);
                        notifications.error('自动备份失败：' + error.message);
                    }
                }

                // 清理旧的自动备份
                cleanupOldAutoBackups() {
                    const autoBackups = this.getAllBackups()
                        .filter(backup => backup.name.startsWith('自动备份_'))
                        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

                    const keepCount = this.autoBackupSettings.keepCount || 10;
                    const toDelete = autoBackups.slice(keepCount);

                    toDelete.forEach(backup => {
                        this.deleteBackup(backup.id);
                    });
                }

                // 获取最后一次自动备份
                getLastAutoBackup() {
                    return this.getAllBackups()
                        .filter(backup => backup.name.startsWith('自动备份_'))
                        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))[0];
                }

                // 更新自动备份设置
                updateAutoBackupSettings(settings) {
                    this.autoBackupSettings = { ...this.autoBackupSettings, ...settings };
                    this.saveAutoBackupSettings();

                    if (settings.enabled) {
                        this.setupAutoBackup();
                    }
                }

                // 计算数据大小
                calculateDataSize(data) {
                    const jsonStr = JSON.stringify(data);
                    return new Blob([jsonStr]).size;
                }

                // 计算校验和
                calculateChecksum(data) {
                    const jsonStr = JSON.stringify(data);
                    let hash = 0;
                    for (let i = 0; i < jsonStr.length; i++) {
                        const char = jsonStr.charCodeAt(i);
                        hash = ((hash << 5) - hash) + char;
                        hash = hash & hash; // 转换为32位整数
                    }
                    return hash.toString(16);
                }

                // 解析CSV用于恢复
                parseCSVForRestore(csvContent) {
                    const lines = csvContent.split('\n');
                    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
                    const data = [];

                    for (let i = 1; i < lines.length; i++) {
                        const line = lines[i].trim();
                        if (!line) continue;

                        const values = this.parseCSVLine(line);
                        const item = {
                            id: Date.now() + i,
                            name: values[0] || `产品${i}`,
                            category: values[1] || 'Electronics',
                            region: values[2] || '北美',
                            channel: values[3] || '跨境电商',
                            platform: values[4] || 'Amazon',
                            quantity: parseInt(values[5]) || 1,
                            price: parseFloat(values[6]) || 0,
                            cost: parseFloat(values[7]) || 0,
                            date: values[8] || new Date().toISOString().slice(0, 10),
                            // 其他默认值...
                        };

                        data.push(item);
                    }

                    return data;
                }

                // 解析CSV行
                parseCSVLine(line) {
                    const result = [];
                    let current = '';
                    let inQuotes = false;

                    for (let i = 0; i < line.length; i++) {
                        const char = line[i];

                        if (char === '"') {
                            inQuotes = !inQuotes;
                        } else if (char === ',' && !inQuotes) {
                            result.push(current.trim());
                            current = '';
                        } else {
                            current += char;
                        }
                    }

                    result.push(current.trim());
                    return result;
                }

                // 保存备份到本地存储
                saveBackups() {
                    try {
                        localStorage.setItem('profit_analysis_backups', JSON.stringify(this.backups));
                    } catch (error) {
                        console.error('保存备份失败:', error);
                        throw new Error('存储空间不足，无法保存备份');
                    }
                }

                // 从本地存储加载备份
                loadBackups() {
                    try {
                        const saved = localStorage.getItem('profit_analysis_backups');
                        return saved ? JSON.parse(saved) : {};
                    } catch (error) {
                        console.error('加载备份失败:', error);
                        return {};
                    }
                }

                // 保存自动备份设置
                saveAutoBackupSettings() {
                    try {
                        localStorage.setItem('profit_analysis_auto_backup', JSON.stringify(this.autoBackupSettings));
                    } catch (error) {
                        console.error('保存自动备份设置失败:', error);
                    }
                }

                // 加载自动备份设置
                loadAutoBackupSettings() {
                    try {
                        const saved = localStorage.getItem('profit_analysis_auto_backup');
                        return saved ? JSON.parse(saved) : {
                            enabled: false,
                            frequency: 'weekly',
                            keepCount: 10
                        };
                    } catch (error) {
                        console.error('加载自动备份设置失败:', error);
                        return {
                            enabled: false,
                            frequency: 'weekly',
                            keepCount: 10
                        };
                    }
                }
            }

            // 初始化备份管理器
            const backupManager = new BackupManager();
            window.backupManager = backupManager;

            // 智能分析系统
            class AnalyticsEngine {
                constructor() {
                    this.analysisResults = {};
                }

                // 运行完整分析
                async runAnalysis(data, options = {}) {
                    const results = {};

                    if (options.trendPrediction) {
                        results.trendPrediction = this.predictTrends(data, options.predictionPeriod);
                    }

                    if (options.anomalyDetection) {
                        results.anomalies = this.detectAnomalies(data);
                    }

                    if (options.profitOptimization) {
                        results.optimization = this.analyzeProfitOptimization(data);
                    }

                    if (options.marketInsights) {
                        results.insights = this.generateMarketInsights(data);
                    }

                    this.analysisResults = results;
                    return results;
                }

                // 趋势预测
                predictTrends(data, days = 30) {
                    const predictions = {};

                    // 按日期分组数据
                    const dailyData = this.groupByDate(data);
                    const dates = Object.keys(dailyData).sort();

                    if (dates.length < 7) {
                        return {
                            error: '数据不足，至少需要7天的数据进行趋势预测'
                        };
                    }

                    // 计算每日指标
                    const dailyMetrics = dates.map(date => {
                        const dayData = dailyData[date];
                        const totalRevenue = dayData.reduce((sum, item) => sum + calculateMetrics(item).revenue, 0);
                        const totalProfit = dayData.reduce((sum, item) => sum + calculateMetrics(item).netProfit, 0);
                        const orderCount = dayData.length;

                        return {
                            date,
                            revenue: totalRevenue,
                            profit: totalProfit,
                            orders: orderCount
                        };
                    });

                    // 简单线性回归预测
                    predictions.revenue = this.linearRegression(dailyMetrics.map(d => d.revenue), days);
                    predictions.profit = this.linearRegression(dailyMetrics.map(d => d.profit), days);
                    predictions.orders = this.linearRegression(dailyMetrics.map(d => d.orders), days);

                    // 计算趋势方向
                    predictions.trends = {
                        revenue: this.calculateTrend(dailyMetrics.map(d => d.revenue)),
                        profit: this.calculateTrend(dailyMetrics.map(d => d.profit)),
                        orders: this.calculateTrend(dailyMetrics.map(d => d.orders))
                    };

                    return predictions;
                }

                // 异常检测
                detectAnomalies(data) {
                    const anomalies = [];

                    // 检测利润率异常
                    const margins = data.map(item => calculateMetrics(item).netProfitMargin);
                    const marginStats = this.calculateStats(margins);

                    data.forEach(item => {
                        const metrics = calculateMetrics(item);
                        const margin = metrics.netProfitMargin;

                        // 利润率异常低
                        if (margin < marginStats.mean - 2 * marginStats.stdDev && margin < 0.1) {
                            anomalies.push({
                                type: 'low_margin',
                                item: item.name,
                                value: margin,
                                severity: 'high',
                                description: `利润率异常低 (${(margin * 100).toFixed(1)}%)，远低于平均水平`
                            });
                        }

                        // 成本异常高
                        if (item.cost > item.price * 0.8) {
                            anomalies.push({
                                type: 'high_cost',
                                item: item.name,
                                value: item.cost / item.price,
                                severity: 'medium',
                                description: `成本占比过高 (${((item.cost / item.price) * 100).toFixed(1)}%)，建议优化成本结构`
                            });
                        }

                        // 销量异常
                        if (item.quantity > 100) {
                            anomalies.push({
                                type: 'high_volume',
                                item: item.name,
                                value: item.quantity,
                                severity: 'low',
                                description: `销量异常高 (${item.quantity}件)，可能是热销产品`
                            });
                        }
                    });

                    return anomalies;
                }

                // 利润优化分析
                analyzeProfitOptimization(data) {
                    const optimization = {
                        recommendations: [],
                        potentialGains: 0
                    };

                    // 按类别分析
                    const categoryData = this.groupByCategory(data);

                    Object.entries(categoryData).forEach(([category, items]) => {
                        const avgMargin = items.reduce((sum, item) =>
                            sum + calculateMetrics(item).netProfitMargin, 0) / items.length;

                        if (avgMargin < 0.15) {
                            const potentialIncrease = items.reduce((sum, item) => {
                                const metrics = calculateMetrics(item);
                                return sum + metrics.revenue * 0.05; // 假设可提升5%
                            }, 0);

                            optimization.recommendations.push({
                                type: 'price_optimization',
                                category: category,
                                impact: potentialIncrease,
                                description: `${category}类别利润率偏低，建议适当提价或降低成本`
                            });

                            optimization.potentialGains += potentialIncrease;
                        }
                    });

                    // 分析低效产品
                    const lowProfitItems = data.filter(item => {
                        const metrics = calculateMetrics(item);
                        return metrics.netProfit < 0;
                    });

                    if (lowProfitItems.length > 0) {
                        const lossAmount = lowProfitItems.reduce((sum, item) =>
                            sum + Math.abs(calculateMetrics(item).netProfit), 0);

                        optimization.recommendations.push({
                            type: 'product_elimination',
                            count: lowProfitItems.length,
                            impact: lossAmount,
                            description: `发现${lowProfitItems.length}个亏损产品，建议停售或重新定价`
                        });
                    }

                    return optimization;
                }

                // 市场洞察
                generateMarketInsights(data) {
                    const insights = [];

                    // 平台表现分析
                    const platformData = this.groupByPlatform(data);
                    const platformPerformance = Object.entries(platformData).map(([platform, items]) => {
                        const totalRevenue = items.reduce((sum, item) => sum + calculateMetrics(item).revenue, 0);
                        const avgMargin = items.reduce((sum, item) => sum + calculateMetrics(item).netProfitMargin, 0) / items.length;

                        return { platform, revenue: totalRevenue, margin: avgMargin, count: items.length };
                    }).sort((a, b) => b.revenue - a.revenue);

                    if (platformPerformance.length > 1) {
                        const bestPlatform = platformPerformance[0];
                        insights.push({
                            type: 'platform_performance',
                            title: '最佳销售平台',
                            content: `${bestPlatform.platform}是表现最好的平台，收入占比${((bestPlatform.revenue / data.reduce((sum, item) => sum + calculateMetrics(item).revenue, 0)) * 100).toFixed(1)}%`,
                            value: bestPlatform.revenue
                        });
                    }

                    // 季节性分析
                    const monthlyData = this.groupByMonth(data);
                    const monthlyRevenue = Object.entries(monthlyData).map(([month, items]) => ({
                        month,
                        revenue: items.reduce((sum, item) => sum + calculateMetrics(item).revenue, 0)
                    })).sort((a, b) => b.revenue - a.revenue);

                    if (monthlyRevenue.length > 1) {
                        const bestMonth = monthlyRevenue[0];
                        insights.push({
                            type: 'seasonality',
                            title: '销售旺季',
                            content: `${bestMonth.month}是销售最好的月份，建议在此期间加大营销投入`,
                            value: bestMonth.revenue
                        });
                    }

                    // 产品组合分析
                    const categoryRevenue = Object.entries(this.groupByCategory(data)).map(([category, items]) => ({
                        category,
                        revenue: items.reduce((sum, item) => sum + calculateMetrics(item).revenue, 0),
                        count: items.length
                    })).sort((a, b) => b.revenue - a.revenue);

                    if (categoryRevenue.length > 0) {
                        const topCategory = categoryRevenue[0];
                        insights.push({
                            type: 'product_mix',
                            title: '核心产品类别',
                            content: `${topCategory.category}是核心类别，贡献了${((topCategory.revenue / data.reduce((sum, item) => sum + calculateMetrics(item).revenue, 0)) * 100).toFixed(1)}%的收入`,
                            value: topCategory.revenue
                        });
                    }

                    return insights;
                }

                // 辅助方法
                groupByDate(data) {
                    return data.reduce((groups, item) => {
                        const date = item.date;
                        if (!groups[date]) groups[date] = [];
                        groups[date].push(item);
                        return groups;
                    }, {});
                }

                groupByCategory(data) {
                    return data.reduce((groups, item) => {
                        const category = item.category;
                        if (!groups[category]) groups[category] = [];
                        groups[category].push(item);
                        return groups;
                    }, {});
                }

                groupByPlatform(data) {
                    return data.reduce((groups, item) => {
                        const platform = item.platform;
                        if (!groups[platform]) groups[platform] = [];
                        groups[platform].push(item);
                        return groups;
                    }, {});
                }

                groupByMonth(data) {
                    return data.reduce((groups, item) => {
                        const month = new Date(item.date).toISOString().slice(0, 7);
                        if (!groups[month]) groups[month] = [];
                        groups[month].push(item);
                        return groups;
                    }, {});
                }

                linearRegression(values, predictDays) {
                    const n = values.length;
                    const x = Array.from({length: n}, (_, i) => i);
                    const y = values;

                    const sumX = x.reduce((a, b) => a + b, 0);
                    const sumY = y.reduce((a, b) => a + b, 0);
                    const sumXY = x.reduce((sum, xi, i) => sum + xi * y[i], 0);
                    const sumXX = x.reduce((sum, xi) => sum + xi * xi, 0);

                    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
                    const intercept = (sumY - slope * sumX) / n;

                    const predictions = [];
                    for (let i = 0; i < predictDays; i++) {
                        const futureX = n + i;
                        predictions.push(Math.max(0, slope * futureX + intercept));
                    }

                    return {
                        slope,
                        intercept,
                        predictions,
                        trend: slope > 0 ? 'increasing' : slope < 0 ? 'decreasing' : 'stable'
                    };
                }

                calculateTrend(values) {
                    if (values.length < 2) return 'insufficient_data';

                    const recent = values.slice(-Math.min(7, values.length));
                    const earlier = values.slice(0, Math.min(7, values.length));

                    const recentAvg = recent.reduce((a, b) => a + b, 0) / recent.length;
                    const earlierAvg = earlier.reduce((a, b) => a + b, 0) / earlier.length;

                    const change = (recentAvg - earlierAvg) / earlierAvg;

                    if (change > 0.1) return 'strong_increase';
                    if (change > 0.05) return 'moderate_increase';
                    if (change < -0.1) return 'strong_decrease';
                    if (change < -0.05) return 'moderate_decrease';
                    return 'stable';
                }

                calculateStats(values) {
                    const mean = values.reduce((a, b) => a + b, 0) / values.length;
                    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
                    const stdDev = Math.sqrt(variance);

                    return { mean, variance, stdDev };
                }
            }

            // 初始化分析引擎
            const analyticsEngine = new AnalyticsEngine();
            window.analyticsEngine = analyticsEngine;

            // 渲染高级图表
            function renderAdvancedChart(data) {
                const chartType = document.getElementById('chart-type-selector')?.value || 'heatmap';
                const dimension = document.getElementById('dimension-selector')?.value || 'category';
                const timeRange = document.getElementById('time-range-selector')?.value || 'all';

                advancedChartManager.renderChart(data, chartType, dimension, timeRange);
            }

            // 产品管理相关变量
            let currentProductsPage = 1;
            const productsPerPage = 6;
            let filteredProducts = [...allData];
            let editingProductId = null;

            // 渲染产品网格
            function renderProductsGrid(data) {
                filteredProducts = [...data];
                const startIndex = (currentProductsPage - 1) * productsPerPage;
                const endIndex = startIndex + productsPerPage;
                const pageProducts = filteredProducts.slice(startIndex, endIndex);

                const gridContainer = document.getElementById('products-grid');
                if (!gridContainer) return;

                if (pageProducts.length === 0) {
                    gridContainer.innerHTML = '<div style="grid-column: 1/-1; text-align: center; padding: 40px; color: #999;">暂无产品数据</div>';
                    return;
                }

                gridContainer.innerHTML = pageProducts.map(product => {
                    const metrics = calculateMetrics(product);
                    const profit = metrics.netProfit;
                    const margin = metrics.netProfitMargin * 100;

                    return `
                        <div class="product-card">
                            <div class="product-card-header">
                                <h4 class="product-name">${product.name}</h4>
                                <div class="product-actions">
                                    <button class="edit-btn" onclick="editProduct(${product.id})">编辑</button>
                                    <button class="delete-btn" onclick="deleteProduct(${product.id})">删除</button>
                                </div>
                            </div>
                            <div class="product-info">
                                <div class="product-info-item">
                                    <span class="product-info-label">类别</span>
                                    <span class="product-info-value">${product.category}</span>
                                </div>
                                <div class="product-info-item">
                                    <span class="product-info-label">平台</span>
                                    <span class="product-info-value">${product.platform}</span>
                                </div>
                                <div class="product-info-item">
                                    <span class="product-info-label">区域</span>
                                    <span class="product-info-value">${product.region}</span>
                                </div>
                                <div class="product-info-item">
                                    <span class="product-info-label">销售价格</span>
                                    <span class="product-info-value">¥${product.price}</span>
                                </div>
                            </div>
                            <div class="product-metrics">
                                <div class="product-metric">
                                    <div class="product-metric-label">销量</div>
                                    <div class="product-metric-value">${product.quantity}</div>
                                </div>
                                <div class="product-metric">
                                    <div class="product-metric-label">收入</div>
                                    <div class="product-metric-value">¥${formatNumber(Math.round(metrics.revenue))}</div>
                                </div>
                                <div class="product-metric">
                                    <div class="product-metric-label">利润</div>
                                    <div class="product-metric-value ${profit >= 0 ? 'profit-positive' : 'profit-negative'}">
                                        ¥${formatNumber(Math.round(profit))}
                                    </div>
                                </div>
                                <div class="product-metric">
                                    <div class="product-metric-label">利润率</div>
                                    <div class="product-metric-value ${margin >= 0 ? 'profit-positive' : 'profit-negative'}">
                                        ${margin.toFixed(1)}%
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');

                renderProductsPagination();
            }

            function renderProductsPagination() {
                const paginationContainer = document.getElementById('products-pagination');
                if (!paginationContainer) return;

                const pageCount = Math.ceil(filteredProducts.length / productsPerPage);
                if (pageCount <= 1) {
                    paginationContainer.innerHTML = '';
                    return;
                }

                let paginationHTML = '';

                // 上一页
                paginationHTML += `<button class="page-btn ${currentProductsPage === 1 ? 'disabled' : ''}"
                    onclick="changeProductsPage(${currentProductsPage - 1})"
                    ${currentProductsPage === 1 ? 'disabled' : ''}>上一页</button>`;

                // 页码
                for (let i = 1; i <= pageCount; i++) {
                    paginationHTML += `<button class="page-btn ${i === currentProductsPage ? 'active' : ''}"
                        onclick="changeProductsPage(${i})">${i}</button>`;
                }

                // 下一页
                paginationHTML += `<button class="page-btn ${currentProductsPage === pageCount ? 'disabled' : ''}"
                    onclick="changeProductsPage(${currentProductsPage + 1})"
                    ${currentProductsPage === pageCount ? 'disabled' : ''}>下一页</button>`;

                paginationContainer.innerHTML = paginationHTML;
            }

            // 全局函数，供HTML调用
            window.changeProductsPage = function(page) {
                const pageCount = Math.ceil(filteredProducts.length / productsPerPage);
                if (page >= 1 && page <= pageCount) {
                    currentProductsPage = page;
                    renderProductsGrid(filteredProducts);
                }
            };

            window.editProduct = function(productId) {
                const product = allData.find(p => p.id === productId);
                if (product) {
                    editingProductId = productId;
                    showProductModal('编辑产品', product);
                }
            };

            window.deleteProduct = function(productId) {
                if (confirm('确定要删除这个产品吗？')) {
                    const index = allData.findIndex(p => p.id === productId);
                    if (index !== -1) {
                        allData.splice(index, 1);
                        currentData = [...allData];
                        render(allData);
                        alert('产品删除成功！');
                    }
                }
            };

            // 显示产品模态框
            function showProductModal(title, product = null) {
                const modal = document.getElementById('product-modal');
                const modalTitle = document.getElementById('modal-title');
                const form = document.getElementById('product-form');

                modalTitle.textContent = title;

                if (product) {
                    // 编辑模式，填充表单
                    document.getElementById('product-name').value = product.name;
                    document.getElementById('product-category').value = product.category;
                    document.getElementById('product-price').value = product.price;
                    document.getElementById('product-cost').value = product.cost;
                    document.getElementById('product-platform').value = product.platform;
                    document.getElementById('product-region').value = product.region;
                    document.getElementById('product-quantity').value = product.quantity;
                    document.getElementById('product-adspend').value = product.adSpend || 0;
                } else {
                    // 新增模式，清空表单
                    form.reset();
                    editingProductId = null;
                }

                modal.style.display = 'flex';
            }

            function hideProductModal() {
                const modal = document.getElementById('product-modal');
                modal.style.display = 'none';
                editingProductId = null;
            }

            // 渲染数据表格
            function renderDataTable(data) {
                const tableBody = document.querySelector('#data-table-body');
                if (!tableBody) return;

                // 更新批量管理器的数据
                batchManager.setAllItems(data);

                if (data.length === 0) {
                    tableBody.innerHTML = `<tr><td colspan="13" style="text-align:center; padding: 20px;">没有找到匹配的数据</td></tr>`;
                    return;
                }

                tableBody.innerHTML = data.map(item => {
                    const { revenue, totalCost, netProfit, netProfitMargin } = calculateMetrics(item);

                    return `
                        <tr>
                            <td class="checkbox-column">
                                <input type="checkbox" value="${item.id}" onchange="handleRowSelection(${item.id}, this.checked)">
                            </td>
                            <td>${item.name}</td>
                            <td>${item.category}</td>
                            <td>${item.region}</td>
                            <td>${item.platform}</td>
                            <td>${item.quantity}</td>
                            <td>¥${formatNumber(item.price)}</td>
                            <td>¥${formatNumber(item.cost)}</td>
                            <td>¥${formatNumber(revenue)}</td>
                            <td class="${netProfit >= 0 ? 'profit-positive' : 'profit-negative'}">¥${formatNumber(netProfit)}</td>
                            <td class="${netProfitMargin >= 0 ? 'profit-positive' : 'profit-negative'}">${(netProfitMargin * 100).toFixed(2)}%</td>
                            <td>${item.date}</td>
                            <td>
                                <button class="btn btn-sm" onclick="editProduct(${item.id})">编辑</button>
                                <button class="btn btn-sm delete-btn" onclick="deleteProduct(${item.id})">删除</button>
                            </td>
                        </tr>
                    `;
                }).join('');
            }

            // 全局函数，供HTML调用
            window.handleRowSelection = function(itemId, isSelected) {
                if (isSelected) {
                    batchManager.selectItem(itemId);
                } else {
                    batchManager.deselectItem(itemId);
                }
            };
            
            // 新增：渲染分页
            function renderPagination(data) {
                const paginationContainer = document.querySelector('.pagination');
                paginationContainer.innerHTML = '';
                const pageCount = Math.ceil(data.length / rowsPerPage);

                if (pageCount <= 1) return; // 如果只有一页或没有数据，不显示分页

                // 上一页按钮
                const prevButton = document.createElement('button');
                prevButton.className = 'page-btn';
                prevButton.textContent = '上一页';
                prevButton.disabled = currentPage === 1;
                prevButton.addEventListener('click', () => {
                    if (currentPage > 1) {
                        currentPage--;
                        render(currentData);
                    }
                });
                paginationContainer.appendChild(prevButton);

                // 页码按钮
                for (let i = 1; i <= pageCount; i++) {
                    const pageButton = document.createElement('button');
                    pageButton.className = 'page-btn' + (i === currentPage ? ' active' : '');
                    pageButton.textContent = i;
                    pageButton.addEventListener('click', () => {
                        currentPage = i;
                        render(currentData);
                    });
                    paginationContainer.appendChild(pageButton);
                }

                // 下一页按钮
                const nextButton = document.createElement('button');
                nextButton.className = 'page-btn';
                nextButton.textContent = '下一页';
                nextButton.disabled = currentPage === pageCount;
                nextButton.addEventListener('click', () => {
                    if (currentPage < pageCount) {
                        currentPage++;
                        render(currentData);
                    }
                });
                paginationContainer.appendChild(nextButton);
            }

            // 3. 交互逻辑 (Interaction Logic)
            const queryBtn = document.getElementById('query-btn');
            const refreshBtn = document.getElementById('refresh-btn');
            const exportBtn = document.getElementById('export-btn');
            const importBtn = document.getElementById('import-btn');
            const importFile = document.getElementById('import-file');

            // 产品管理相关元素
            const addProductBtn = document.getElementById('add-product-btn');
            const productModal = document.getElementById('product-modal');
            const modalClose = document.getElementById('modal-close');
            const cancelBtn = document.getElementById('cancel-btn');
            const productForm = document.getElementById('product-form');
            const productFilterBtn = document.getElementById('product-filter-btn');
            const productSearch = document.getElementById('product-search');

            // 客户管理相关元素
            const customerFilterBtn = document.getElementById('customer-filter-btn');
            const customerSearch = document.getElementById('customer-search');

            // 高级筛选相关元素
            const advancedFilterBtn = document.getElementById('advanced-filter-btn');
            const saveFilterBtn = document.getElementById('save-filter-btn');
            const loadFilterBtn = document.getElementById('load-filter-btn');
            const advancedFilterModal = document.getElementById('advanced-filter-modal');
            const saveFilterModal = document.getElementById('save-filter-modal');
            const loadFilterModal = document.getElementById('load-filter-modal');

            const dateStartInput = document.getElementById('date-start');
            const dateEndInput = document.getElementById('date-end');
            const categorySelect = document.getElementById('category-select');
            const regionSelect = document.getElementById('region-select');
            const channelSelect = document.getElementById('channel-select');
            const platformSelect = document.getElementById('platform-select');

            // 查询按钮点击事件
            queryBtn.addEventListener('click', () => {
                const filters = {
                    startDate: dateStartInput.value,
                    endDate: dateEndInput.value,
                    category: categorySelect.value,
                    region: regionSelect.value,
                    channel: channelSelect.value,
                    platform: platformSelect.value
                };
                
                const filteredData = allData.filter(item => {
                    const itemDate = new Date(item.date);
                    const startDate = filters.startDate ? new Date(filters.startDate) : null;
                    const endDate = filters.endDate ? new Date(filters.endDate) : null;

                    if (startDate && itemDate < startDate) return false;
                    if (endDate && itemDate > endDate) return false;
                    if (filters.category !== 'all' && item.category !== filters.category) return false;
                    if (filters.region !== 'all' && item.region !== filters.region) return false;
                    if (filters.channel !== 'all' && item.channel !== filters.channel) return false;
                    if (filters.platform !== 'all' && item.platform !== filters.platform) return false;
                    
                    return true;
                });
                
                currentData = filteredData; // 更新当前数据
                currentPage = 1; // 重置到第一页
                render(filteredData);
            });

            // 刷新按钮点击事件
            refreshBtn.addEventListener('click', () => {
                // 重置筛选条件
                dateStartInput.value = '2023-01-01';
                dateEndInput.value = '2023-12-31';
                categorySelect.value = 'all';
                regionSelect.value = 'all';
                channelSelect.value = 'all';
                platformSelect.value = 'all';
                
                currentData = [...allData]; // 重置当前数据
                currentPage = 1; // 重置到第一页
                render(allData);
            });

            // 导出CSV功能
            exportBtn.addEventListener('click', () => {
                exportToCSV(currentData);
            });

            // 导入数据功能
            importBtn.addEventListener('click', () => {
                importFile.click();
            });

            importFile.addEventListener('change', (event) => {
                const file = event.target.files[0];
                if (file) {
                    importFromFile(file);
                }
            });

            function exportToCSV(data) {
                const headers = ['产品名称', '产品类别', '销售区域', '销售渠道', '销售平台', '销售数量', '单价(元)', '采购成本(元)', '总收入(元)', '商品总成本(元)', '平台佣金(元)', 'FBA总费用(元)', '仓储总费用(元)', '长期仓储费(元)', '退货成本(元)', '移除费(元)', '入库运费(元)', '广告花费(元)', '税费(元)', '总成本(元)', '净利润(元)', '净利润率'];
                const csvRows = [];
                csvRows.push(headers.join(','));

                data.forEach(item => {
                    const { revenue, productCost, referralFee, fbaFee, storageFee, longTermStorageFee, returnFee, removalFee, inboundShipping, taxes, closingFee, totalCost, netProfit, netProfitMargin } = calculateMetrics(item);
                    
                    const row = [
                        item.name,
                        item.category,
                        item.region,
                        item.channel,
                        item.platform,
                        item.quantity,
                        item.price,
                        item.cost,
                        Math.round(revenue),
                        Math.round(productCost),
                        Math.round(referralFee),
                        Math.round(fbaFee),
                        Math.round(storageFee),
                        Math.round(longTermStorageFee),
                        Math.round(returnFee),
                        Math.round(removalFee),
                        Math.round(inboundShipping),
                        Math.round(item.adSpend),
                        Math.round(taxes),
                        Math.round(totalCost),
                        Math.round(netProfit),
                        `"${(netProfitMargin*100).toFixed(1)}%"`
                    ].join(',');
                    csvRows.push(row);
                });

                // BOM for UTF-8
                const bom = new Uint8Array([0xEF, 0xBB, 0xBF]);
                const csvString = csvRows.join('\n');
                const blob = new Blob([bom, csvString], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.setAttribute('download', 'profit_analysis_data.csv');
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }

            // 导入数据功能
            function importFromFile(file) {
                const fileName = file.name.toLowerCase();

                if (fileName.endsWith('.csv')) {
                    importFromCSV(file);
                } else if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
                    importFromExcel(file);
                } else {
                    alert('不支持的文件格式，请选择CSV或Excel文件。');
                }
            }

            function importFromCSV(file) {
                loading.show('正在导入数据...');

                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const csv = e.target.result;
                        const lines = csv.split('\n');

                        if (lines.length < 2) {
                            loading.hide();
                            notifications.error('CSV文件至少需要包含标题行和一行数据');
                            return;
                        }

                        const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));

                        // 验证必需的列
                        const requiredColumns = ['产品名称', '价格', '成本'];
                        const missingColumns = requiredColumns.filter(col =>
                            !headers.some(header => header.includes(col.substring(0, 2)))
                        );

                        if (missingColumns.length > 0) {
                            loading.hide();
                            notifications.error(`CSV文件缺少必需的列：${missingColumns.join(', ')}`);
                            return;
                        }

                        const importedData = [];
                        const errors = [];
                        let maxId = Math.max(...allData.map(item => item.id), 0);

                        for (let i = 1; i < lines.length; i++) {
                            const line = lines[i].trim();
                            if (!line) continue;

                            try {
                                const values = parseCSVLine(line);
                                if (values.length < 3) {
                                    errors.push(`第${i + 1}行：数据不完整`);
                                    continue;
                                }

                                const name = values[0]?.trim();
                                const price = parseFloat(values[6]) || parseFloat(values[1]);
                                const cost = parseFloat(values[7]) || parseFloat(values[2]);

                                // 数据验证
                                if (!name || name.length < 2) {
                                    errors.push(`第${i + 1}行：产品名称无效`);
                                    continue;
                                }

                                if (isNaN(price) || price <= 0) {
                                    errors.push(`第${i + 1}行：价格无效`);
                                    continue;
                                }

                                if (isNaN(cost) || cost < 0) {
                                    errors.push(`第${i + 1}行：成本无效`);
                                    continue;
                                }

                                const item = {
                                    id: ++maxId,
                                    name: name,
                                    category: values[1] || 'Electronics',
                                    region: values[2] || '北美',
                                    channel: values[3] || '跨境电商',
                                    platform: values[4] || 'Amazon',
                                    quantity: Math.max(1, parseInt(values[5]) || 1),
                                    price: price,
                                    cost: cost,
                                    date: values[8] || new Date().toISOString().slice(0, 10),
                                    sizeCategory: 'standard',
                                    adSpend: Math.max(0, parseFloat(values[9]) || 0),
                                    returnRate: Math.min(1, Math.max(0, parseFloat(values[10]) || 0.03)),
                                    exchangeRateLoss: 0.02,
                                    customsDutyRate: 0.05,
                                    crossBorderLogisticsFee: 3.5,
                                    paymentProcessingRate: 0.029,
                                    inboundPlacementFee: 0.27,
                                    lowInventoryFee: 0,
                                    storageUtilizationSurcharge: 0,
                                    removalFee: 0,
                                    inboundShipping: 0,
                                    taxes: 0,
                                    storageFee: 1,
                                    longTermStorageFee: 0,
                                    closingFee: 0,
                                    fbaFee: 3.5
                                };

                                importedData.push(item);

                            } catch (rowError) {
                                errors.push(`第${i + 1}行：解析错误 - ${rowError.message}`);
                            }
                        }

                        setTimeout(() => {
                            loading.hide();

                            if (importedData.length > 0) {
                                // 数据完整性检查
                                const integrityResult = validateDataIntegrity(importedData);
                                if (!integrityResult.valid) {
                                    notifications.warning(`数据导入完成，但发现以下问题：\n${integrityResult.errors.join('\n')}`);
                                }

                                allData.push(...importedData);
                                currentData = [...allData];
                                currentPage = 1;
                                render(allData);

                                let message = `成功导入 ${importedData.length} 条数据！`;
                                if (errors.length > 0) {
                                    message += `\n跳过 ${errors.length} 条无效数据。`;
                                }
                                notifications.success(message);

                                if (errors.length > 0 && errors.length <= 10) {
                                    // 显示前10个错误
                                    notifications.warning(`导入错误详情：\n${errors.slice(0, 10).join('\n')}`);
                                }
                            } else {
                                notifications.error('没有找到有效的数据行。请检查文件格式。');
                            }
                        }, 1000);

                    } catch (error) {
                        loading.hide();
                        console.error('导入错误:', error);
                        notifications.error('文件格式错误，请检查CSV文件格式：' + error.message);
                    }
                };

                reader.onerror = function() {
                    loading.hide();
                    notifications.error('文件读取失败，请重试');
                };

                reader.readAsText(file, 'UTF-8');
            }

            // Excel导入功能
            function importFromExcel(file) {
                loading.show('正在导入Excel文件...');

                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        // 简化的Excel解析 - 实际项目中建议使用SheetJS等库
                        const data = new Uint8Array(e.target.result);

                        // 这里简化处理，实际应该使用专门的Excel解析库
                        // 目前转换为提示用户使用CSV格式
                        loading.hide();
                        notifications.warning('Excel导入功能需要额外的库支持。请将Excel文件另存为CSV格式后重新导入。');

                        // 如果要完整支持Excel，需要添加SheetJS库：
                        // const workbook = XLSX.read(data, {type: 'array'});
                        // const worksheet = workbook.Sheets[workbook.SheetNames[0]];
                        // const jsonData = XLSX.utils.sheet_to_json(worksheet);
                        // processImportedData(jsonData);

                    } catch (error) {
                        loading.hide();
                        console.error('Excel导入失败:', error);
                        notifications.error('Excel文件解析失败：' + error.message);
                    }
                };

                reader.onerror = function() {
                    loading.hide();
                    notifications.error('文件读取失败');
                };

                reader.readAsArrayBuffer(file);
            }

            // 解析CSV行，处理引号内的逗号
            function parseCSVLine(line) {
                const result = [];
                let current = '';
                let inQuotes = false;

                for (let i = 0; i < line.length; i++) {
                    const char = line[i];

                    if (char === '"') {
                        inQuotes = !inQuotes;
                    } else if (char === ',' && !inQuotes) {
                        result.push(current.trim());
                        current = '';
                    } else {
                        current += char;
                    }
                }

                result.push(current.trim());
                return result;
            }

            // 产品管理事件监听器
            if (addProductBtn) {
                addProductBtn.addEventListener('click', () => {
                    showProductModal('添加产品');
                });
            }

            if (modalClose) {
                modalClose.addEventListener('click', hideProductModal);
            }

            if (cancelBtn) {
                cancelBtn.addEventListener('click', hideProductModal);
            }

            if (productModal) {
                productModal.addEventListener('click', (e) => {
                    if (e.target === productModal) {
                        hideProductModal();
                    }
                });
            }

            if (productForm) {
                productForm.addEventListener('submit', (e) => {
                    e.preventDefault();
                    saveProduct();
                });
            }

            if (productFilterBtn) {
                productFilterBtn.addEventListener('click', filterProducts);
            }

            if (productSearch) {
                productSearch.addEventListener('input', filterProducts);
            }

            // 客户管理事件监听器
            if (customerFilterBtn) {
                customerFilterBtn.addEventListener('click', filterCustomers);
            }

            if (customerSearch) {
                customerSearch.addEventListener('input', filterCustomers);
            }

            // 高级筛选事件监听器
            if (advancedFilterBtn) {
                advancedFilterBtn.addEventListener('click', showAdvancedFilterModal);
            }

            if (saveFilterBtn) {
                saveFilterBtn.addEventListener('click', showSaveFilterModal);
            }

            if (loadFilterBtn) {
                loadFilterBtn.addEventListener('click', showLoadFilterModal);
            }

            // 高级筛选模态框事件
            if (advancedFilterModal) {
                document.getElementById('filter-modal-close')?.addEventListener('click', hideAdvancedFilterModal);
                document.getElementById('cancel-filter-btn')?.addEventListener('click', hideAdvancedFilterModal);
                document.getElementById('reset-filter-btn')?.addEventListener('click', resetAdvancedFilter);
                document.getElementById('apply-filter-btn')?.addEventListener('click', applyAdvancedFilter);

                advancedFilterModal.addEventListener('click', (e) => {
                    if (e.target === advancedFilterModal) {
                        hideAdvancedFilterModal();
                    }
                });
            }

            // 保存筛选模态框事件
            if (saveFilterModal) {
                document.getElementById('save-filter-modal-close')?.addEventListener('click', hideSaveFilterModal);
                document.getElementById('cancel-save-filter-btn')?.addEventListener('click', hideSaveFilterModal);
                document.getElementById('confirm-save-filter-btn')?.addEventListener('click', confirmSaveFilter);

                saveFilterModal.addEventListener('click', (e) => {
                    if (e.target === saveFilterModal) {
                        hideSaveFilterModal();
                    }
                });
            }

            // 加载筛选模态框事件
            if (loadFilterModal) {
                document.getElementById('load-filter-modal-close')?.addEventListener('click', hideLoadFilterModal);
                document.getElementById('cancel-load-filter-btn')?.addEventListener('click', hideLoadFilterModal);

                loadFilterModal.addEventListener('click', (e) => {
                    if (e.target === loadFilterModal) {
                        hideLoadFilterModal();
                    }
                });
            }

            // 高级图表事件监听器
            const updateChartBtn = document.getElementById('update-chart-btn');
            const exportChartBtn = document.getElementById('export-chart-btn');
            const fullscreenBtn = document.getElementById('fullscreen-btn');
            const downloadBtn = document.getElementById('download-btn');
            const drillUpBtn = document.getElementById('drill-up-btn');
            const resetDrillBtn = document.getElementById('reset-drill-btn');

            if (updateChartBtn) {
                updateChartBtn.addEventListener('click', () => {
                    renderAdvancedChart(currentData);
                });
            }

            if (exportChartBtn) {
                exportChartBtn.addEventListener('click', () => {
                    advancedChartManager.exportChart();
                });
            }

            if (fullscreenBtn) {
                fullscreenBtn.addEventListener('click', () => {
                    advancedChartManager.toggleFullscreen();
                });
            }

            if (downloadBtn) {
                downloadBtn.addEventListener('click', () => {
                    advancedChartManager.exportChart();
                });
            }

            if (drillUpBtn) {
                drillUpBtn.addEventListener('click', () => {
                    advancedChartManager.drillUp();
                });
            }

            if (resetDrillBtn) {
                resetDrillBtn.addEventListener('click', () => {
                    advancedChartManager.resetDrill();
                });
            }

            // 图表类型选择器变化事件
            const chartTypeSelector = document.getElementById('chart-type-selector');
            if (chartTypeSelector) {
                chartTypeSelector.addEventListener('change', () => {
                    renderAdvancedChart(currentData);
                });
            }

            // 批量操作事件监听器
            const selectAllCheckbox = document.getElementById('select-all-checkbox');
            const headerSelectAll = document.getElementById('header-select-all');
            const batchEditBtn = document.getElementById('batch-edit-btn');
            const batchExportBtn = document.getElementById('batch-export-btn');
            const batchDeleteBtn = document.getElementById('batch-delete-btn');
            const validateDataBtn = document.getElementById('validate-data-btn');
            const duplicateCheckBtn = document.getElementById('duplicate-check-btn');
            const batchEditModal = document.getElementById('batch-edit-modal');

            if (selectAllCheckbox) {
                selectAllCheckbox.addEventListener('change', () => {
                    batchManager.toggleSelectAll();
                });
            }

            if (headerSelectAll) {
                headerSelectAll.addEventListener('change', () => {
                    batchManager.toggleSelectAll();
                });
            }

            if (batchEditBtn) {
                batchEditBtn.addEventListener('click', showBatchEditModal);
            }

            if (batchExportBtn) {
                batchExportBtn.addEventListener('click', () => {
                    batchManager.batchExport();
                });
            }

            if (batchDeleteBtn) {
                batchDeleteBtn.addEventListener('click', () => {
                    batchManager.batchDelete();
                });
            }

            if (validateDataBtn) {
                validateDataBtn.addEventListener('click', () => {
                    batchManager.validateData();
                });
            }

            if (duplicateCheckBtn) {
                duplicateCheckBtn.addEventListener('click', () => {
                    batchManager.checkDuplicates();
                });
            }

            // 备份和恢复事件监听器
            const backupDataBtn = document.getElementById('backup-data-btn');
            const restoreDataBtn = document.getElementById('restore-data-btn');
            const backupModal = document.getElementById('backup-modal');
            const restoreModal = document.getElementById('restore-modal');

            if (backupDataBtn) {
                backupDataBtn.addEventListener('click', showBackupModal);
            }

            if (restoreDataBtn) {
                restoreDataBtn.addEventListener('click', showRestoreModal);
            }

            // 备份模态框事件
            if (backupModal) {
                document.getElementById('backup-modal-close')?.addEventListener('click', hideBackupModal);
                document.getElementById('create-backup-btn')?.addEventListener('click', createBackup);
                document.getElementById('auto-backup-enabled')?.addEventListener('change', toggleAutoBackupConfig);
                document.getElementById('save-auto-backup-btn')?.addEventListener('click', saveAutoBackupSettings);

                backupModal.addEventListener('click', (e) => {
                    if (e.target === backupModal) {
                        hideBackupModal();
                    }
                });
            }

            // 恢复模态框事件
            if (restoreModal) {
                document.getElementById('restore-modal-close')?.addEventListener('click', hideRestoreModal);
                document.getElementById('select-restore-file-btn')?.addEventListener('click', selectRestoreFile);
                document.getElementById('restore-from-file-btn')?.addEventListener('click', restoreFromFile);

                restoreModal.addEventListener('click', (e) => {
                    if (e.target === restoreModal) {
                        hideRestoreModal();
                    }
                });
            }

            // 恢复文件选择事件
            const restoreFileInput = document.getElementById('restore-file');
            if (restoreFileInput) {
                restoreFileInput.addEventListener('change', handleRestoreFileSelect);
            }

            // 批量编辑模态框事件
            if (batchEditModal) {
                document.getElementById('batch-edit-modal-close')?.addEventListener('click', hideBatchEditModal);
                document.getElementById('cancel-batch-edit-btn')?.addEventListener('click', hideBatchEditModal);
                document.getElementById('confirm-batch-edit-btn')?.addEventListener('click', confirmBatchEdit);

                batchEditModal.addEventListener('click', (e) => {
                    if (e.target === batchEditModal) {
                        hideBatchEditModal();
                    }
                });

                // 批量编辑字段启用/禁用逻辑
                setupBatchEditFieldToggle('edit-category-check', 'batch-category');
                setupBatchEditFieldToggle('edit-platform-check', 'batch-platform');
                setupBatchEditFieldToggle('edit-region-check', 'batch-region');
                setupBatchEditFieldToggle('edit-price-check', ['price-adjustment-type', 'price-adjustment-value']);

                // 价格调整类型变化事件
                const priceAdjustmentType = document.getElementById('price-adjustment-type');
                const adjustmentUnit = document.getElementById('adjustment-unit');
                if (priceAdjustmentType && adjustmentUnit) {
                    priceAdjustmentType.addEventListener('change', () => {
                        adjustmentUnit.textContent = priceAdjustmentType.value === 'percentage' ? '%' : '元';
                    });
                }
            }

            function setupBatchEditFieldToggle(checkboxId, fieldIds) {
                const checkbox = document.getElementById(checkboxId);
                const fields = Array.isArray(fieldIds) ? fieldIds.map(id => document.getElementById(id)) : [document.getElementById(fieldIds)];

                if (checkbox && fields.every(field => field)) {
                    checkbox.addEventListener('change', () => {
                        fields.forEach(field => {
                            field.disabled = !checkbox.checked;
                        });
                    });
                }
            }

            function showBatchEditModal() {
                const selectedCount = batchManager.selectedItems.size;
                if (selectedCount === 0) {
                    notifications.warning('请先选择要编辑的项目');
                    return;
                }

                document.getElementById('batch-edit-count').textContent = selectedCount;
                if (batchEditModal) {
                    batchEditModal.style.display = 'flex';
                }
            }

            function hideBatchEditModal() {
                if (batchEditModal) {
                    batchEditModal.style.display = 'none';
                    // 重置表单
                    document.querySelectorAll('#batch-edit-modal input[type="checkbox"]').forEach(cb => cb.checked = false);
                    document.querySelectorAll('#batch-edit-modal select, #batch-edit-modal input[type="number"]').forEach(field => {
                        field.disabled = true;
                        field.value = '';
                    });
                }
            }

            function confirmBatchEdit() {
                const editData = {};

                // 收集编辑数据
                if (document.getElementById('edit-category-check').checked) {
                    editData.category = document.getElementById('batch-category').value;
                }

                if (document.getElementById('edit-platform-check').checked) {
                    editData.platform = document.getElementById('batch-platform').value;
                }

                if (document.getElementById('edit-region-check').checked) {
                    editData.region = document.getElementById('batch-region').value;
                }

                if (document.getElementById('edit-price-check').checked) {
                    const type = document.getElementById('price-adjustment-type').value;
                    const value = parseFloat(document.getElementById('price-adjustment-value').value);

                    if (!isNaN(value)) {
                        editData.priceAdjustment = { type, value };
                    }
                }

                // 验证编辑数据
                if (Object.keys(editData).length === 0) {
                    notifications.warning('请至少选择一个要编辑的字段');
                    return;
                }

                // 执行批量编辑
                batchManager.batchEdit(editData);
                hideBatchEditModal();
            }

            // 备份和恢复UI处理函数
            function showBackupModal() {
                updateBackupInfo();
                if (backupModal) {
                    backupModal.style.display = 'flex';
                }
            }

            function hideBackupModal() {
                if (backupModal) {
                    backupModal.style.display = 'none';
                }
            }

            function showRestoreModal() {
                renderBackupList();
                if (restoreModal) {
                    restoreModal.style.display = 'flex';
                }
            }

            function hideRestoreModal() {
                if (restoreModal) {
                    restoreModal.style.display = 'none';
                }
            }

            function updateBackupInfo() {
                const currentDataCount = document.getElementById('current-data-count');
                const backupSizeEstimate = document.getElementById('backup-size-estimate');

                if (currentDataCount) {
                    currentDataCount.textContent = allData.length;
                }

                if (backupSizeEstimate) {
                    const size = backupManager.calculateDataSize(allData);
                    backupSizeEstimate.textContent = `约 ${formatFileSize(size)}`;
                }
            }

            function createBackup() {
                const name = document.getElementById('backup-name').value.trim();
                const description = document.getElementById('backup-description').value.trim();

                if (!name) {
                    notifications.error('请输入备份名称');
                    return;
                }

                try {
                    loading.show('创建备份中...');

                    setTimeout(() => {
                        backupManager.createBackup(name, description);
                        loading.hide();
                        hideBackupModal();
                        notifications.success('备份创建成功');
                    }, 1000);

                } catch (error) {
                    loading.hide();
                    notifications.error('创建备份失败：' + error.message);
                }
            }

            function renderBackupList() {
                const container = document.getElementById('backup-list');
                if (!container) return;

                const backups = backupManager.getAllBackups();

                if (backups.length === 0) {
                    container.innerHTML = '<div class="empty-backups">暂无备份数据</div>';
                    return;
                }

                container.innerHTML = backups.map(backup => `
                    <div class="backup-item" data-backup-id="${backup.id}">
                        <div class="backup-item-header">
                            <div class="backup-name">${backup.name}</div>
                            <div class="backup-date">${new Date(backup.createdAt).toLocaleString()}</div>
                        </div>
                        <div class="backup-description">${backup.description || '无描述'}</div>
                        <div class="backup-meta">
                            <span>数据量：${backup.data.length} 条</span>
                            <span>大小：${formatFileSize(backup.size)}</span>
                        </div>
                        <div class="backup-actions">
                            <button onclick="restoreFromBackup('${backup.id}')">恢复</button>
                            <button onclick="exportBackup('${backup.id}')">导出</button>
                            <button class="delete-btn" onclick="deleteBackup('${backup.id}')">删除</button>
                        </div>
                    </div>
                `).join('');
            }

            // 全局函数
            window.restoreFromBackup = function(backupId) {
                if (!confirm('确定要恢复这个备份吗？这将替换当前所有数据。')) {
                    return;
                }

                try {
                    loading.show('正在恢复备份...');

                    setTimeout(() => {
                        backupManager.restoreFromBackup(backupId);
                        loading.hide();
                        hideRestoreModal();
                        notifications.success('备份恢复成功');
                    }, 1000);

                } catch (error) {
                    loading.hide();
                    notifications.error('恢复失败：' + error.message);
                }
            };

            window.exportBackup = function(backupId) {
                try {
                    backupManager.exportBackup(backupId);
                    notifications.success('备份导出成功');
                } catch (error) {
                    notifications.error('导出失败：' + error.message);
                }
            };

            window.deleteBackup = function(backupId) {
                if (confirm('确定要删除这个备份吗？')) {
                    try {
                        backupManager.deleteBackup(backupId);
                        renderBackupList();
                        notifications.success('备份删除成功');
                    } catch (error) {
                        notifications.error('删除失败：' + error.message);
                    }
                }
            };

            // 文件大小格式化函数
            function formatFileSize(bytes) {
                if (bytes === 0) return '0 B';
                const k = 1024;
                const sizes = ['B', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            // 备份和恢复UI处理函数
            function showBackupModal() {
                updateBackupInfo();
                loadAutoBackupSettings();
                if (backupModal) {
                    backupModal.style.display = 'flex';
                }
            }

            function hideBackupModal() {
                if (backupModal) {
                    backupModal.style.display = 'none';
                    // 重置表单
                    document.getElementById('backup-name').value = '';
                    document.getElementById('backup-description').value = '';
                }
            }

            function showRestoreModal() {
                renderBackupList();
                if (restoreModal) {
                    restoreModal.style.display = 'flex';
                }
            }

            function hideRestoreModal() {
                if (restoreModal) {
                    restoreModal.style.display = 'none';
                    // 重置文件选择
                    const restoreFileInput = document.getElementById('restore-file');
                    const restoreFileInfo = document.getElementById('restore-file-info');
                    const restoreFromFileBtn = document.getElementById('restore-from-file-btn');

                    if (restoreFileInput) restoreFileInput.value = '';
                    if (restoreFileInfo) restoreFileInfo.style.display = 'none';
                    if (restoreFromFileBtn) restoreFromFileBtn.style.display = 'none';
                }
            }

            function updateBackupInfo() {
                const currentDataCount = document.getElementById('current-data-count');
                const backupSizeEstimate = document.getElementById('backup-size-estimate');

                if (currentDataCount) {
                    currentDataCount.textContent = allData.length;
                }

                if (backupSizeEstimate) {
                    const size = backupManager.calculateDataSize(allData);
                    backupSizeEstimate.textContent = `约 ${formatFileSize(size)}`;
                }
            }

            function createBackup() {
                const name = document.getElementById('backup-name').value.trim();
                const description = document.getElementById('backup-description').value.trim();

                if (!name) {
                    notifications.error('请输入备份名称');
                    return;
                }

                try {
                    loading.show('创建备份中...');

                    setTimeout(() => {
                        const backupId = backupManager.createBackup(name, description);
                        loading.hide();
                        hideBackupModal();
                        notifications.success('备份创建成功');
                    }, 1000);

                } catch (error) {
                    loading.hide();
                    console.error('创建备份失败:', error);
                    notifications.error('创建备份失败：' + error.message);
                }
            }

            function toggleAutoBackupConfig() {
                const checkbox = document.getElementById('auto-backup-enabled');
                const config = document.getElementById('auto-backup-config');

                if (checkbox && config) {
                    config.style.display = checkbox.checked ? 'block' : 'none';
                }
            }

            function loadAutoBackupSettings() {
                const settings = backupManager.autoBackupSettings;

                const enabledCheckbox = document.getElementById('auto-backup-enabled');
                const frequencySelect = document.getElementById('auto-backup-frequency');
                const keepInput = document.getElementById('auto-backup-keep');

                if (enabledCheckbox) {
                    enabledCheckbox.checked = settings.enabled;
                    toggleAutoBackupConfig();
                }

                if (frequencySelect) {
                    frequencySelect.value = settings.frequency;
                }

                if (keepInput) {
                    keepInput.value = settings.keepCount;
                }
            }

            function saveAutoBackupSettings() {
                const enabled = document.getElementById('auto-backup-enabled').checked;
                const frequency = document.getElementById('auto-backup-frequency').value;
                const keepCount = parseInt(document.getElementById('auto-backup-keep').value);

                try {
                    backupManager.updateAutoBackupSettings({
                        enabled,
                        frequency,
                        keepCount
                    });

                    notifications.success('自动备份设置已保存');
                } catch (error) {
                    console.error('保存自动备份设置失败:', error);
                    notifications.error('保存设置失败：' + error.message);
                }
            }

            function renderBackupList() {
                const container = document.getElementById('backup-list');
                if (!container) return;

                const backups = backupManager.getAllBackups();

                if (backups.length === 0) {
                    container.innerHTML = '<div class="empty-backups">暂无备份数据</div>';
                    return;
                }

                container.innerHTML = backups.map(backup => `
                    <div class="backup-item" data-backup-id="${backup.id}">
                        <div class="backup-item-header">
                            <div class="backup-name">${backup.name}</div>
                            <div class="backup-date">${new Date(backup.createdAt).toLocaleString()}</div>
                        </div>
                        <div class="backup-description">${backup.description || '无描述'}</div>
                        <div class="backup-meta">
                            <span>数据量：${backup.data.length} 条</span>
                            <span>大小：${formatFileSize(backup.size)}</span>
                            <span>版本：${backup.version}</span>
                        </div>
                        <div class="backup-actions">
                            <button onclick="restoreFromBackup('${backup.id}')">恢复</button>
                            <button onclick="exportBackup('${backup.id}')">导出</button>
                            <button class="delete-btn" onclick="deleteBackup('${backup.id}')">删除</button>
                        </div>
                    </div>
                `).join('');
            }

            function selectRestoreFile() {
                const restoreFileInput = document.getElementById('restore-file');
                if (restoreFileInput) {
                    restoreFileInput.click();
                }
            }

            function handleRestoreFileSelect(event) {
                const file = event.target.files[0];
                if (!file) return;

                const restoreFileInfo = document.getElementById('restore-file-info');
                const restoreFileName = document.getElementById('restore-file-name');
                const restoreFileSize = document.getElementById('restore-file-size');
                const restoreFromFileBtn = document.getElementById('restore-from-file-btn');

                if (restoreFileName) restoreFileName.textContent = file.name;
                if (restoreFileSize) restoreFileSize.textContent = formatFileSize(file.size);
                if (restoreFileInfo) restoreFileInfo.style.display = 'flex';
                if (restoreFromFileBtn) restoreFromFileBtn.style.display = 'block';
            }

            function restoreFromFile() {
                const restoreFileInput = document.getElementById('restore-file');
                const file = restoreFileInput?.files[0];

                if (!file) {
                    notifications.error('请先选择要恢复的文件');
                    return;
                }

                if (!confirm('确定要从文件恢复数据吗？这将替换当前所有数据，此操作不可撤销。')) {
                    return;
                }

                try {
                    loading.show('正在恢复数据...');

                    backupManager.restoreFromFile(file)
                        .then(count => {
                            loading.hide();
                            hideRestoreModal();
                            notifications.success(`成功恢复 ${count} 条数据`);
                        })
                        .catch(error => {
                            loading.hide();
                            console.error('恢复失败:', error);
                            notifications.error('恢复失败：' + error.message);
                        });

                } catch (error) {
                    loading.hide();
                    console.error('恢复失败:', error);
                    notifications.error('恢复失败：' + error.message);
                }
            }

            // 全局函数，供HTML调用
            window.restoreFromBackup = function(backupId) {
                if (!confirm('确定要恢复这个备份吗？这将替换当前所有数据，此操作不可撤销。')) {
                    return;
                }

                try {
                    loading.show('正在恢复备份...');

                    setTimeout(() => {
                        backupManager.restoreFromBackup(backupId);
                        loading.hide();
                        hideRestoreModal();
                        notifications.success('备份恢复成功');
                    }, 1000);

                } catch (error) {
                    loading.hide();
                    console.error('恢复备份失败:', error);
                    notifications.error('恢复失败：' + error.message);
                }
            };

            window.exportBackup = function(backupId) {
                try {
                    backupManager.exportBackup(backupId);
                    notifications.success('备份导出成功');
                } catch (error) {
                    console.error('导出备份失败:', error);
                    notifications.error('导出失败：' + error.message);
                }
            };

            window.deleteBackup = function(backupId) {
                if (confirm('确定要删除这个备份吗？此操作不可撤销。')) {
                    try {
                        backupManager.deleteBackup(backupId);
                        renderBackupList();
                        notifications.success('备份删除成功');
                    } catch (error) {
                        console.error('删除备份失败:', error);
                        notifications.error('删除失败：' + error.message);
                    }
                }
            };

            // 文件大小格式化函数
            function formatFileSize(bytes) {
                if (bytes === 0) return '0 B';
                const k = 1024;
                const sizes = ['B', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            // 智能分析事件监听器
            const runAnalysisBtn = document.getElementById('run-analysis-btn');
            const exportAnalysisBtn = document.getElementById('export-analysis-btn');

            if (runAnalysisBtn) {
                runAnalysisBtn.addEventListener('click', runIntelligentAnalysis);
            }

            if (exportAnalysisBtn) {
                exportAnalysisBtn.addEventListener('click', exportAnalysisReport);
            }

            // 智能分析功能
            async function runIntelligentAnalysis() {
                try {
                    loading.show('正在进行智能分析...');

                    // 获取分析选项
                    const options = {
                        trendPrediction: document.getElementById('trend-prediction')?.checked || false,
                        anomalyDetection: document.getElementById('anomaly-detection')?.checked || false,
                        profitOptimization: document.getElementById('profit-optimization')?.checked || false,
                        marketInsights: document.getElementById('market-insights')?.checked || false,
                        predictionPeriod: parseInt(document.getElementById('prediction-period')?.value) || 30,
                        confidenceLevel: parseFloat(document.getElementById('confidence-level')?.value) || 0.9
                    };

                    // 模拟分析延迟
                    setTimeout(async () => {
                        try {
                            const results = await analyticsEngine.runAnalysis(currentData, options);
                            renderAnalysisResults(results);
                            loading.hide();
                            notifications.success('智能分析完成');
                        } catch (error) {
                            loading.hide();
                            console.error('分析失败:', error);
                            notifications.error('分析失败：' + error.message);
                        }
                    }, 2000);

                } catch (error) {
                    loading.hide();
                    console.error('启动分析失败:', error);
                    notifications.error('启动分析失败：' + error.message);
                }
            }

            // 渲染分析结果
            function renderAnalysisResults(results) {
                const container = document.getElementById('analysis-results');
                if (!container) return;

                let html = '';

                // 趋势预测
                if (results.trendPrediction) {
                    html += renderTrendPrediction(results.trendPrediction);
                }

                // 异常检测
                if (results.anomalies) {
                    html += renderAnomalies(results.anomalies);
                }

                // 利润优化
                if (results.optimization) {
                    html += renderOptimization(results.optimization);
                }

                // 市场洞察
                if (results.insights) {
                    html += renderInsights(results.insights);
                }

                container.innerHTML = html;
            }

            function renderTrendPrediction(prediction) {
                if (prediction.error) {
                    return `
                        <div class="analysis-section">
                            <h3><span class="section-icon">📈</span>趋势预测</h3>
                            <div class="insight-card warning">
                                <div class="insight-title">数据不足</div>
                                <div class="insight-content">${prediction.error}</div>
                            </div>
                        </div>
                    `;
                }

                const trendNames = {
                    'strong_increase': '强劲上升',
                    'moderate_increase': '温和上升',
                    'stable': '保持稳定',
                    'moderate_decrease': '温和下降',
                    'strong_decrease': '明显下降'
                };

                return `
                    <div class="analysis-section">
                        <h3><span class="section-icon">📈</span>趋势预测</h3>
                        <div class="predictions-list">
                            <div class="prediction-item">
                                <span class="prediction-label">收入趋势</span>
                                <span class="prediction-value">${trendNames[prediction.trends.revenue] || '未知'}</span>
                            </div>
                            <div class="prediction-item">
                                <span class="prediction-label">利润趋势</span>
                                <span class="prediction-value">${trendNames[prediction.trends.profit] || '未知'}</span>
                            </div>
                        </div>
                    </div>
                `;
            }

            function renderAnomalies(anomalies) {
                if (anomalies.length === 0) {
                    return `
                        <div class="analysis-section">
                            <h3><span class="section-icon">🔍</span>异常检测</h3>
                            <div class="insight-card positive">
                                <div class="insight-title">未发现异常</div>
                                <div class="insight-content">所有数据指标均在正常范围内</div>
                            </div>
                        </div>
                    `;
                }

                return `
                    <div class="analysis-section">
                        <h3><span class="section-icon">🔍</span>异常检测</h3>
                        <div class="anomalies-list">
                            ${anomalies.slice(0, 5).map(anomaly => `
                                <div class="anomaly-item">
                                    <div class="anomaly-title">${anomaly.item}</div>
                                    <div class="anomaly-description">${anomaly.description}</div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            }

            function renderOptimization(optimization) {
                return `
                    <div class="analysis-section">
                        <h3><span class="section-icon">💡</span>利润优化建议</h3>
                        <div class="insights-grid">
                            <div class="insight-card positive">
                                <div class="insight-title">潜在收益</div>
                                <div class="insight-content">
                                    通过优化可增加：<span class="insight-value">¥${formatNumber(Math.round(optimization.potentialGains))}</span>
                                </div>
                            </div>
                        </div>
                        <div class="recommendations-list">
                            ${optimization.recommendations.slice(0, 3).map(rec => `
                                <div class="recommendation-item">
                                    <div class="recommendation-title">${rec.description}</div>
                                    <div class="recommendation-impact">
                                        预期影响：¥${formatNumber(Math.round(rec.impact))}
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            }

            function renderInsights(insights) {
                return `
                    <div class="analysis-section">
                        <h3><span class="section-icon">🎯</span>市场洞察</h3>
                        <div class="insights-grid">
                            ${insights.map(insight => `
                                <div class="insight-card info">
                                    <div class="insight-title">${insight.title}</div>
                                    <div class="insight-content">
                                        ${insight.content}
                                        <br><span class="insight-value">¥${formatNumber(Math.round(insight.value))}</span>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            }

            // 导出分析报告
            function exportAnalysisReport() {
                const results = analyticsEngine.analysisResults;
                if (!results || Object.keys(results).length === 0) {
                    notifications.warning('请先运行分析');
                    return;
                }

                try {
                    const report = {
                        title: '利润分析智能报告',
                        generatedAt: new Date().toISOString(),
                        dataRange: {
                            totalRecords: currentData.length
                        },
                        analysis: results
                    };

                    const dataStr = JSON.stringify(report, null, 2);
                    const blob = new Blob([dataStr], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);

                    const link = document.createElement('a');
                    link.download = `analysis_report_${Date.now()}.json`;
                    link.href = url;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    URL.revokeObjectURL(url);
                    notifications.success('分析报告导出成功');

                } catch (error) {
                    console.error('导出报告失败:', error);
                    notifications.error('导出失败：' + error.message);
                }
            }

            function saveProduct() {
                try {
                    // 表单验证
                    const validationResult = validator.validateForm('product-form');
                    if (!validationResult.valid) {
                        notifications.error('请检查表单中的错误信息');
                        return;
                    }

                    loading.show('保存产品中...');

                    const formData = new FormData(productForm);

                    // 额外的业务逻辑验证
                    const price = parseFloat(formData.get('price'));
                    const cost = parseFloat(formData.get('cost'));

                    if (cost > price) {
                        loading.hide();
                        notifications.warning('采购成本不应高于销售价格，这可能导致亏损');
                        // 不阻止保存，只是警告
                    }

                    const productData = {
                        name: formData.get('name').trim(),
                        category: formData.get('category'),
                        price: price,
                        cost: cost,
                        platform: formData.get('platform'),
                        region: formData.get('region'),
                        quantity: parseInt(formData.get('quantity')) || 1,
                        adSpend: parseFloat(formData.get('adSpend')) || 0,
                        channel: '跨境电商',
                        date: new Date().toISOString().slice(0, 10),
                        sizeCategory: 'standard',
                        returnRate: 0.03,
                        exchangeRateLoss: 0.02,
                        customsDutyRate: 0.05,
                        crossBorderLogisticsFee: 3.5,
                        paymentProcessingRate: 0.029,
                        inboundPlacementFee: 0.27,
                        lowInventoryFee: 0,
                        storageUtilizationSurcharge: 0,
                        removalFee: 0,
                        inboundShipping: 0,
                        taxes: 0,
                        storageFee: 1,
                        longTermStorageFee: 0,
                        closingFee: 0,
                        fbaFee: 3.5
                    };

                    // 检查产品名称是否重复（除了编辑的产品本身）
                    const duplicateProduct = allData.find(p =>
                        p.name.toLowerCase() === productData.name.toLowerCase() &&
                        p.id !== editingProductId
                    );

                    if (duplicateProduct) {
                        loading.hide();
                        notifications.warning('已存在同名产品，建议使用不同的产品名称');
                        // 不阻止保存，只是警告
                    }

                    setTimeout(() => { // 模拟异步操作
                        try {
                            // 检查产品名称唯一性
                            const productName = productData.name.toLowerCase().trim();
                            const existingProduct = allData.find(item =>
                                item.name.toLowerCase().trim() === productName &&
                                item.id !== editingProductId
                            );

                            if (existingProduct) {
                                loading.hide();
                                notifications.error(`产品名称 "${productData.name}" 已存在，请使用不同的名称`);
                                return;
                            }

                            if (editingProductId) {
                                // 编辑模式
                                const index = allData.findIndex(p => p.id === editingProductId);
                                if (index !== -1) {
                                    allData[index] = { ...allData[index], ...productData };
                                    notifications.success('产品更新成功！');
                                } else {
                                    throw new Error('找不到要编辑的产品');
                                }
                            } else {
                                // 新增模式
                                const maxId = Math.max(...allData.map(item => item.id), 0);
                                productData.id = maxId + 1;
                                allData.push(productData);
                                notifications.success('产品添加成功！');
                            }

                            currentData = [...allData];
                            render(allData);
                            hideProductModal();

                        } catch (error) {
                            console.error('保存产品时出错:', error);
                            notifications.error('保存失败：' + error.message);
                        } finally {
                            loading.hide();
                        }
                    }, 500);

                } catch (error) {
                    loading.hide();
                    console.error('保存产品时出错:', error);
                    notifications.error('保存失败，请检查输入信息');
                }
            }

            function filterProducts() {
                const searchTerm = productSearch ? productSearch.value.toLowerCase() : '';
                const categoryFilter = document.getElementById('product-category-filter')?.value || 'all';
                const platformFilter = document.getElementById('product-platform-filter')?.value || 'all';

                filteredProducts = allData.filter(product => {
                    const matchesSearch = !searchTerm || product.name.toLowerCase().includes(searchTerm);
                    const matchesCategory = categoryFilter === 'all' || product.category === categoryFilter;
                    const matchesPlatform = platformFilter === 'all' || product.platform === platformFilter;

                    return matchesSearch && matchesCategory && matchesPlatform;
                });

                currentProductsPage = 1;
                renderProductsGrid(filteredProducts);
            }

            // 客户筛选功能
            function filterCustomers() {
                const searchTerm = document.getElementById('customer-search')?.value.toLowerCase() || '';
                const regionFilter = document.getElementById('customer-region-filter')?.value || 'all';
                const valueFilter = document.getElementById('customer-value-filter')?.value || 'all';

                filteredCustomers = customersData.filter(customer => {
                    const matchesSearch = customer.id.toLowerCase().includes(searchTerm) ||
                                        customer.region.toLowerCase().includes(searchTerm);
                    const matchesRegion = regionFilter === 'all' || customer.region === regionFilter;
                    const matchesValue = valueFilter === 'all' || customer.valueLevel === valueFilter;

                    return matchesSearch && matchesRegion && matchesValue;
                });

                currentCustomersPage = 1;
                renderCustomersTable(filteredCustomers);
            }

            // 客户管理相关变量
            let currentCustomersPage = 1;
            const customersPerPage = 10;
            let filteredCustomers = [];
            let customersData = [];

            // 生成客户数据
            function generateCustomersData(salesData) {
                const customerMap = new Map();

                salesData.forEach(item => {
                    // 为每个销售记录生成客户ID（基于区域和平台）
                    const customerId = `${item.region}-${item.platform}-${Math.floor(Math.random() * 1000) + 1}`;

                    if (!customerMap.has(customerId)) {
                        customerMap.set(customerId, {
                            id: customerId,
                            region: item.region,
                            platform: item.platform,
                            orders: [],
                            totalSpent: 0,
                            orderCount: 0,
                            lastOrderDate: item.date,
                            firstOrderDate: item.date
                        });
                    }

                    const customer = customerMap.get(customerId);
                    customer.orders.push(item);
                    customer.totalSpent += calculateMetrics(item).revenue;
                    customer.orderCount++;

                    // 更新日期
                    if (new Date(item.date) > new Date(customer.lastOrderDate)) {
                        customer.lastOrderDate = item.date;
                    }
                    if (new Date(item.date) < new Date(customer.firstOrderDate)) {
                        customer.firstOrderDate = item.date;
                    }
                });

                // 计算客户价值等级
                const customers = Array.from(customerMap.values()).map(customer => {
                    customer.avgOrderValue = customer.totalSpent / customer.orderCount;

                    // 客户价值分级
                    if (customer.totalSpent > 10000) {
                        customer.valueLevel = 'high';
                        customer.valueLevelText = '高价值客户';
                    } else if (customer.totalSpent > 3000) {
                        customer.valueLevel = 'medium';
                        customer.valueLevelText = '中等价值客户';
                    } else {
                        customer.valueLevel = 'low';
                        customer.valueLevelText = '低价值客户';
                    }

                    return customer;
                });

                return customers;
            }

            // 生成客户数据
            function generateCustomersData(data) {
                const customersMap = {};

                data.forEach(item => {
                    const customerId = `${item.region}-${Math.floor(item.id / 3) + 1}`;
                    const { revenue, totalCost } = calculateMetrics(item);
                    const profit = revenue - totalCost;

                    if (!customersMap[customerId]) {
                        customersMap[customerId] = {
                            id: customerId,
                            region: item.region,
                            orders: [],
                            totalRevenue: 0,
                            totalProfit: 0,
                            platforms: new Set(),
                            lastOrderDate: item.date
                        };
                    }

                    const customer = customersMap[customerId];
                    customer.orders.push({
                        id: item.id,
                        date: item.date,
                        revenue: revenue,
                        profit: profit,
                        platform: item.platform
                    });
                    customer.totalRevenue += revenue;
                    customer.totalProfit += profit;
                    customer.platforms.add(item.platform);

                    if (new Date(item.date) > new Date(customer.lastOrderDate)) {
                        customer.lastOrderDate = item.date;
                    }
                });

                return Object.values(customersMap).map(customer => ({
                    ...customer,
                    orderCount: customer.orders.length,
                    avgOrderValue: customer.totalRevenue / customer.orders.length,
                    platforms: Array.from(customer.platforms),
                    mainPlatform: Array.from(customer.platforms)[0],
                    valueLevel: getCustomerValueLevel(customer.totalRevenue, customer.orders.length)
                }));
            }

            function getCustomerValueLevel(totalRevenue, orderCount) {
                if (totalRevenue > 50000 || orderCount > 5) return 'high';
                if (totalRevenue > 20000 || orderCount > 2) return 'medium';
                return 'low';
            }

            function renderCustomersData(data) {
                const customers = generateCustomersData(data);
                filteredCustomers = [...customers];

                renderCustomersStats(customers);
                renderCustomersCharts(customers);
                renderCustomersTable(customers);
            }

            function renderCustomersStats(customers) {
                const totalCustomers = customers.length;
                const activeCustomers = customers.filter(c => {
                    const daysSinceLastOrder = (new Date() - new Date(c.lastOrderDate)) / (1000 * 60 * 60 * 24);
                    return daysSinceLastOrder <= 30;
                }).length;
                const avgOrderValue = customers.reduce((sum, c) => sum + c.avgOrderValue, 0) / customers.length || 0;

                updateElement('total-customers', totalCustomers);
                updateElement('active-customers', activeCustomers);
                updateElement('avg-order-value', `¥${formatNumber(Math.round(avgOrderValue))}`);
            }

            function updateElement(id, value) {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                }
            }

            function renderCustomersCharts(customers) {
                renderCustomerRegionChart(customers);
                renderCustomerValueChart(customers);
            }

            function renderCustomerRegionChart(customers) {
                const chartDom = document.getElementById('customer-region-chart');
                if (!chartDom) return;

                chartDom.innerHTML = '';
                const myChart = echarts.init(chartDom);

                const regionData = {};
                customers.forEach(customer => {
                    if (!regionData[customer.region]) {
                        regionData[customer.region] = 0;
                    }
                    regionData[customer.region]++;
                });

                const chartData = Object.keys(regionData).map(key => ({
                    name: key,
                    value: regionData[key]
                }));

                const option = {
                    tooltip: { trigger: 'item', formatter: '{a} <br/>{b}: {c} ({d}%)' },
                    legend: { orient: 'vertical', left: 'left' },
                    series: [
                        {
                            name: '客户区域分布',
                            type: 'pie',
                            radius: '70%',
                            center: ['60%', '50%'],
                            data: chartData,
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            }
                        }
                    ]
                };
                myChart.setOption(option);
                window.customerRegionChart = myChart;
            }

            function renderCustomerValueChart(customers) {
                const chartDom = document.getElementById('customer-value-chart');
                if (!chartDom) return;

                chartDom.innerHTML = '';
                const myChart = echarts.init(chartDom);

                const valueData = { high: 0, medium: 0, low: 0 };
                customers.forEach(customer => {
                    valueData[customer.valueLevel]++;
                });

                const chartData = [
                    { name: '高价值客户', value: valueData.high },
                    { name: '中等价值客户', value: valueData.medium },
                    { name: '低价值客户', value: valueData.low }
                ];

                const option = {
                    tooltip: { trigger: 'item', formatter: '{a} <br/>{b}: {c} ({d}%)' },
                    legend: { orient: 'vertical', left: 'left' },
                    series: [
                        {
                            name: '客户价值分析',
                            type: 'pie',
                            radius: ['40%', '70%'],
                            center: ['60%', '50%'],
                            data: chartData,
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            }
                        }
                    ]
                };
                myChart.setOption(option);
                window.customerValueChart = myChart;
            }

            function renderCustomersTable(customers) {
                const startIndex = (currentCustomersPage - 1) * customersPerPage;
                const endIndex = startIndex + customersPerPage;
                const pageCustomers = filteredCustomers.slice(startIndex, endIndex);

                const tableBody = document.getElementById('customers-table-body');
                if (!tableBody) return;

                if (pageCustomers.length === 0) {
                    tableBody.innerHTML = '<tr><td colspan="8" style="text-align: center; padding: 20px;">暂无客户数据</td></tr>';
                    return;
                }

                tableBody.innerHTML = pageCustomers.map(customer => {
                    const valueClass = `customer-value-${customer.valueLevel}`;
                    const valueName = {
                        'high': '高价值',
                        'medium': '中等价值',
                        'low': '低价值'
                    }[customer.valueLevel];

                    return `
                        <tr>
                            <td>${customer.id}</td>
                            <td>${customer.region}</td>
                            <td>${customer.orderCount}</td>
                            <td>¥${formatNumber(Math.round(customer.totalRevenue))}</td>
                            <td>¥${formatNumber(Math.round(customer.avgOrderValue))}</td>
                            <td>${new Date(customer.lastOrderDate).toLocaleDateString('zh-CN')}</td>
                            <td><span class="${valueClass}">${valueName}</span></td>
                            <td>${customer.mainPlatform}</td>
                        </tr>
                    `;
                }).join('');

                renderCustomersPagination();
            }

            function renderCustomersPagination() {
                const paginationContainer = document.getElementById('customers-pagination');
                if (!paginationContainer) return;

                const pageCount = Math.ceil(filteredCustomers.length / customersPerPage);
                if (pageCount <= 1) {
                    paginationContainer.innerHTML = '';
                    return;
                }

                let paginationHTML = '';

                // 上一页
                paginationHTML += `<button class="page-btn ${currentCustomersPage === 1 ? 'disabled' : ''}"
                    onclick="changeCustomersPage(${currentCustomersPage - 1})"
                    ${currentCustomersPage === 1 ? 'disabled' : ''}>上一页</button>`;

                // 页码
                for (let i = 1; i <= pageCount; i++) {
                    paginationHTML += `<button class="page-btn ${i === currentCustomersPage ? 'active' : ''}"
                        onclick="changeCustomersPage(${i})">${i}</button>`;
                }

                // 下一页
                paginationHTML += `<button class="page-btn ${currentCustomersPage === pageCount ? 'disabled' : ''}"
                    onclick="changeCustomersPage(${currentCustomersPage + 1})"
                    ${currentCustomersPage === pageCount ? 'disabled' : ''}>下一页</button>`;

                paginationContainer.innerHTML = paginationHTML;
            }

            // 全局函数，供HTML调用
            window.changeCustomersPage = function(page) {
                const pageCount = Math.ceil(filteredCustomers.length / customersPerPage);
                if (page >= 1 && page <= pageCount) {
                    currentCustomersPage = page;
                    renderCustomersTable(filteredCustomers);
                }
            };

            // 客户筛选功能
            function filterCustomers() {
                const customers = generateCustomersData(allData);
                const searchTerm = document.getElementById('customer-search')?.value.toLowerCase() || '';
                const regionFilter = document.getElementById('customer-region-filter')?.value || 'all';
                const valueFilter = document.getElementById('customer-value-filter')?.value || 'all';

                filteredCustomers = customers.filter(customer => {
                    const matchesSearch = !searchTerm ||
                        customer.id.toLowerCase().includes(searchTerm) ||
                        customer.region.toLowerCase().includes(searchTerm);
                    const matchesRegion = regionFilter === 'all' || customer.region === regionFilter;
                    const matchesValue = valueFilter === 'all' || customer.valueLevel === valueFilter;

                    return matchesSearch && matchesRegion && matchesValue;
                });

                currentCustomersPage = 1;
                renderCustomersTable(filteredCustomers);
            }

            // 高级筛选功能函数
            function showAdvancedFilterModal() {
                if (advancedFilterModal) {
                    advancedFilterModal.style.display = 'flex';
                }
            }

            function hideAdvancedFilterModal() {
                if (advancedFilterModal) {
                    advancedFilterModal.style.display = 'none';
                }
            }

            function showSaveFilterModal() {
                const currentFilters = getCurrentFilters();
                if (Object.keys(currentFilters).length === 0) {
                    notifications.warning('请先设置筛选条件');
                    return;
                }
                if (saveFilterModal) {
                    saveFilterModal.style.display = 'flex';
                }
            }

            function hideSaveFilterModal() {
                if (saveFilterModal) {
                    saveFilterModal.style.display = 'none';
                    document.getElementById('filter-name').value = '';
                    document.getElementById('filter-description').value = '';
                }
            }

            function showLoadFilterModal() {
                renderSavedFiltersList();
                if (loadFilterModal) {
                    loadFilterModal.style.display = 'flex';
                }
            }

            function hideLoadFilterModal() {
                if (loadFilterModal) {
                    loadFilterModal.style.display = 'none';
                }
            }

            function resetAdvancedFilter() {
                // 重置所有筛选字段
                document.getElementById('revenue-min').value = '';
                document.getElementById('revenue-max').value = '';
                document.getElementById('profit-min').value = '';
                document.getElementById('profit-max').value = '';
                document.getElementById('margin-min').value = '';
                document.getElementById('margin-max').value = '';
                document.getElementById('quantity-min').value = '';
                document.getElementById('quantity-max').value = '';

                // 重置复选框
                document.querySelectorAll('#category-checkboxes input[type="checkbox"]').forEach(cb => cb.checked = false);
                document.querySelectorAll('#platform-checkboxes input[type="checkbox"]').forEach(cb => cb.checked = false);

                // 重置排序
                document.getElementById('sort-field').value = '';
                document.getElementById('sort-direction').value = 'desc';
            }

            function getCurrentFilters() {
                const filters = {};

                // 数值范围筛选
                const revenueMin = parseFloat(document.getElementById('revenue-min').value);
                const revenueMax = parseFloat(document.getElementById('revenue-max').value);
                const profitMin = parseFloat(document.getElementById('profit-min').value);
                const profitMax = parseFloat(document.getElementById('profit-max').value);
                const marginMin = parseFloat(document.getElementById('margin-min').value);
                const marginMax = parseFloat(document.getElementById('margin-max').value);
                const quantityMin = parseInt(document.getElementById('quantity-min').value);
                const quantityMax = parseInt(document.getElementById('quantity-max').value);

                if (!isNaN(revenueMin)) filters.revenueMin = revenueMin;
                if (!isNaN(revenueMax)) filters.revenueMax = revenueMax;
                if (!isNaN(profitMin)) filters.profitMin = profitMin;
                if (!isNaN(profitMax)) filters.profitMax = profitMax;
                if (!isNaN(marginMin)) filters.marginMin = marginMin;
                if (!isNaN(marginMax)) filters.marginMax = marginMax;
                if (!isNaN(quantityMin)) filters.quantityMin = quantityMin;
                if (!isNaN(quantityMax)) filters.quantityMax = quantityMax;

                // 多选筛选
                const selectedCategories = Array.from(document.querySelectorAll('#category-checkboxes input[type="checkbox"]:checked')).map(cb => cb.value);
                const selectedPlatforms = Array.from(document.querySelectorAll('#platform-checkboxes input[type="checkbox"]:checked')).map(cb => cb.value);

                if (selectedCategories.length > 0) filters.categories = selectedCategories;
                if (selectedPlatforms.length > 0) filters.platforms = selectedPlatforms;

                // 排序设置
                const sortField = document.getElementById('sort-field').value;
                const sortDirection = document.getElementById('sort-direction').value;

                if (sortField) {
                    filters.sortField = sortField;
                    filters.sortDirection = sortDirection;
                }

                return filters;
            }

            function applyAdvancedFilter() {
                try {
                    loading.show('应用筛选条件...');

                    const filters = getCurrentFilters();
                    filterManager.currentFilters = filters;

                    setTimeout(() => {
                        let filteredData = filterManager.applyFilters(allData, filters);

                        if (filters.sortField) {
                            filteredData = filterManager.sortData(filteredData, filters.sortField, filters.sortDirection);
                        }

                        currentData = filteredData;
                        currentPage = 1;
                        render(filteredData);

                        hideAdvancedFilterModal();
                        loading.hide();

                        notifications.success(`筛选完成，找到 ${filteredData.length} 条匹配数据`);
                    }, 500);

                } catch (error) {
                    loading.hide();
                    console.error('应用筛选时出错:', error);
                    notifications.error('筛选失败：' + error.message);
                }
            }

            function confirmSaveFilter() {
                const name = document.getElementById('filter-name').value.trim();
                const description = document.getElementById('filter-description').value.trim();

                if (!name) {
                    notifications.error('请输入筛选条件名称');
                    return;
                }

                const filters = getCurrentFilters();
                if (Object.keys(filters).length === 0) {
                    notifications.error('没有设置任何筛选条件');
                    return;
                }

                try {
                    const filterId = filterManager.saveFilter(name, description, filters);
                    hideSaveFilterModal();
                    notifications.success('筛选条件保存成功');
                } catch (error) {
                    console.error('保存筛选条件时出错:', error);
                    notifications.error('保存失败：' + error.message);
                }
            }

            function renderSavedFiltersList() {
                const container = document.getElementById('saved-filters-list');
                if (!container) return;

                const savedFilters = filterManager.getSavedFilters();

                if (savedFilters.length === 0) {
                    container.innerHTML = '<div class="empty-filters">暂无保存的筛选条件</div>';
                    return;
                }

                container.innerHTML = savedFilters.map(filter => `
                    <div class="saved-filter-item" data-filter-id="${filter.id}">
                        <div class="saved-filter-name">${filter.name}</div>
                        <div class="saved-filter-description">${filter.description || '无描述'}</div>
                        <div class="saved-filter-meta">
                            <span>创建时间：${new Date(filter.createdAt).toLocaleDateString()}</span>
                            <span>使用次数：${filter.usageCount}</span>
                        </div>
                        <div class="saved-filter-actions">
                            <button onclick="loadSavedFilter('${filter.id}')">加载</button>
                            <button class="delete-btn" onclick="deleteSavedFilter('${filter.id}')">删除</button>
                        </div>
                    </div>
                `).join('');
            }

            // 全局函数，供HTML调用
            window.loadSavedFilter = function(filterId) {
                try {
                    const filters = filterManager.loadFilter(filterId);
                    if (filters) {
                        applyLoadedFilters(filters);
                        hideLoadFilterModal();
                        notifications.success('筛选条件加载成功');
                    } else {
                        notifications.error('筛选条件不存在');
                    }
                } catch (error) {
                    console.error('加载筛选条件时出错:', error);
                    notifications.error('加载失败：' + error.message);
                }
            };

            window.deleteSavedFilter = function(filterId) {
                if (confirm('确定要删除这个筛选条件吗？')) {
                    try {
                        filterManager.deleteFilter(filterId);
                        renderSavedFiltersList();
                        notifications.success('筛选条件删除成功');
                    } catch (error) {
                        console.error('删除筛选条件时出错:', error);
                        notifications.error('删除失败：' + error.message);
                    }
                }
            };

            function applyLoadedFilters(filters) {
                // 应用数值范围筛选
                document.getElementById('revenue-min').value = filters.revenueMin || '';
                document.getElementById('revenue-max').value = filters.revenueMax || '';
                document.getElementById('profit-min').value = filters.profitMin || '';
                document.getElementById('profit-max').value = filters.profitMax || '';
                document.getElementById('margin-min').value = filters.marginMin || '';
                document.getElementById('margin-max').value = filters.marginMax || '';
                document.getElementById('quantity-min').value = filters.quantityMin || '';
                document.getElementById('quantity-max').value = filters.quantityMax || '';

                // 应用多选筛选
                document.querySelectorAll('#category-checkboxes input[type="checkbox"]').forEach(cb => {
                    cb.checked = filters.categories && filters.categories.includes(cb.value);
                });
                document.querySelectorAll('#platform-checkboxes input[type="checkbox"]').forEach(cb => {
                    cb.checked = filters.platforms && filters.platforms.includes(cb.value);
                });

                // 应用排序设置
                document.getElementById('sort-field').value = filters.sortField || '';
                document.getElementById('sort-direction').value = filters.sortDirection || 'desc';

                // 立即应用筛选
                applyAdvancedFilter();
            }

            // 新增：排序逻辑
            document.querySelectorAll('.data-table th').forEach((headerCell, index) => {
                headerCell.addEventListener('click', () => {
                    const columnKey = headerCell.getAttribute('data-column');
                    if (!columnKey) return;
                    
                    if (sortColumn === columnKey) {
                        sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
                    } else {
                        sortColumn = columnKey;
                        sortDirection = 'asc';
                    }

                    sortData(columnKey, sortDirection);
                    currentPage = 1; // 排序后回到第一页
                    render(currentData);
                    updateSortIndicators();
                });
            });
            
            function getColumnValue(item, key) {
                const { revenue, productCost, referralFee, fbaFee, storageFee, longTermStorageFee, returnFee, removalFee, inboundShipping, taxes, closingFee, totalCost, netProfit, netProfitMargin, adSpend } = calculateMetrics(item);
            
                switch (key) {
                    case 'name': return item.name;
                    case 'revenue': return revenue;
                    case 'productCost': return productCost;
                    case 'referralFee': return referralFee;
                    case 'fbaFee': return fbaFee;
                    case 'adSpend': return adSpend;
                    case 'totalCost': return totalCost;
                    case 'netProfit': return netProfit;
                    case 'netProfitMargin': return netProfitMargin;
                    case 'storageFee': return storageFee;
                    case 'returnFee': return returnFee;
                    case 'removalFee': return removalFee;
                    case 'inboundShipping': return inboundShipping;
                    case 'taxes': return taxes;
                    case 'closingFee': return item.quantity * item.closingFee;
                    case 'longTermStorageFee': return longTermStorageFee;
                    default: return 0;
                }
            }
            
            function sortData(key, direction) {
                currentData.sort((a, b) => {
                    const valA = getColumnValue(a, key);
                    const valB = getColumnValue(b, key);
            
                    if (typeof valA === 'string') {
                        return direction === 'asc' ? valA.localeCompare(valB) : valB.localeCompare(valA);
                    } else {
                        return direction === 'asc' ? valA - valB : valB - valA;
                    }
                });
            }
            
            function updateSortIndicators() {
                document.querySelectorAll('.data-table th').forEach(headerCell => {
                    const indicator = headerCell.querySelector('.sort-indicator');
                    if(indicator) indicator.remove(); // 移除旧的指示器

                    const columnKey = headerCell.getAttribute('data-column');
                    if(columnKey === sortColumn) {
                        const newIndicator = document.createElement('span');
                        newIndicator.className = 'sort-indicator';
                        newIndicator.textContent = sortDirection === 'asc' ? '▲' : '▼';
                        headerCell.appendChild(newIndicator);
                    }
                });
            }

            // Tab切换逻辑
            const tabBtns = document.querySelectorAll('.tab-btn');
            const tabContents = document.querySelectorAll('.tab-content');
            tabBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    tabBtns.forEach(b => b.classList.remove('active'));
                    btn.classList.add('active');
                    const tab = btn.getAttribute('data-tab');
                    tabContents.forEach(content => {
                        if (content.id === 'tab-' + tab) {
                            content.style.display = 'block';
                        } else {
                            content.style.display = 'none';
                        }
                    });

                    // 图表重绘 - 解决标签页切换后图表不显示的问题
                    setTimeout(() => {
                        if (tab === 'today') {
                            if (window.todayPlatformChart) window.todayPlatformChart.resize();
                            if (window.todayProductsChart) window.todayProductsChart.resize();
                        } else if (tab === 'trend') {
                            if (window.trendChart) window.trendChart.resize();
                            if (window.platformChart) window.platformChart.resize();
                        } else if (tab === 'category') {
                            if (window.categoryChart) window.categoryChart.resize();
                            if (window.regionChart) window.regionChart.resize();
                        } else if (tab === 'advanced-charts') {
                            if (window.advancedChartManager && window.advancedChartManager.currentChart) {
                                window.advancedChartManager.currentChart.resize();
                            }
                        } else if (tab === 'analytics') {
                            // 智能分析标签页激活时的处理
                            console.log('智能分析标签页已激活');
                        } else if (tab === 'customers') {
                            if (window.customerRegionChart) window.customerRegionChart.resize();
                            if (window.customerValueChart) window.customerValueChart.resize();
                        }
                    }, 100);
                });
            });

            // 4. 初始化验证和渲染 (Initialization and Initial Render)
            // 首先验证数据完整性
            const validationResult = validateDataIntegrity(allData);
            if (!validationResult.valid) {
                console.warn('数据完整性检查发现问题:', validationResult.errors);
                notifications.warning(`数据完整性检查发现 ${validationResult.errors.length} 个问题，请检查数据质量`);
            } else {
                notifications.success('数据完整性检查通过');
            }

            setupProductFormValidation();
            render(allData);

            // 显示数据统计信息
            console.log(`系统已加载 ${allData.length} 条数据记录`);
            notifications.info(`系统已加载 ${allData.length} 条数据记录`);

            // 5. 响应式图表重绘
            window.addEventListener('resize', () => {
                setTimeout(() => {
                    // 重绘所有图表
                    if (window.trendChart) window.trendChart.resize();
                    if (window.categoryChart) window.categoryChart.resize();
                    if (window.platformChart) window.platformChart.resize();
                    if (window.regionChart) window.regionChart.resize();
                    if (window.todayPlatformChart) window.todayPlatformChart.resize();
                    if (window.todayProductsChart) window.todayProductsChart.resize();
                    if (window.customerRegionChart) window.customerRegionChart.resize();
                    if (window.customerValueChart) window.customerValueChart.resize();
                }, 100);
            });
        });
